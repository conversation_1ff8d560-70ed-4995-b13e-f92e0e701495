/**
 * @license React
 * react-dom-static.node.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("stream"),ba=require("util"),fa=require("async_hooks"),pa=require("next/dist/compiled/react-experimental"),qa=require("react-dom");function ra(a){"function"===typeof a.flush&&a.flush()}var h=null,k=0,sa=!0;
function p(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<k&&(ta(a,h.subarray(0,k)),h=new Uint8Array(2048),k=0),ta(a,ya.encode(b));else{var c=h;0<k&&(c=h.subarray(k));c=ya.encodeInto(b,c);var d=c.read;k+=c.written;d<b.length&&(ta(a,h.subarray(0,k)),h=new Uint8Array(2048),k=ya.encodeInto(b.slice(d),h).written);2048===k&&(ta(a,h),h=new Uint8Array(2048),k=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<k&&(ta(a,h.subarray(0,k)),h=new Uint8Array(2048),k=0),ta(a,b)):(c=h.length-k,c<
b.byteLength&&(0===c?ta(a,h):(h.set(b.subarray(0,c),k),k+=c,ta(a,h),b=b.subarray(c)),h=new Uint8Array(2048),k=0),h.set(b,k),k+=b.byteLength,2048===k&&(ta(a,h),h=new Uint8Array(2048),k=0)))}function ta(a,b){a=a.write(b);sa=sa&&a}function v(a,b){p(a,b);return sa}function za(a){h&&0<k&&a.write(h.subarray(0,k));h=null;k=0;sa=!0}var ya=new ba.TextEncoder;function w(a){return ya.encode(a)}
var Aa=Object.assign,x=Object.prototype.hasOwnProperty,Ba=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ca={},Da={};
function Ea(a){if(x.call(Da,a))return!0;if(x.call(Ca,a))return!1;if(Ba.test(a))return Da[a]=!0;Ca[a]=!0;return!1}
var Ka=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),La=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ma=/["'&<>]/;
function y(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ma.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Na=/([A-Z])/g,Oa=/^ms-/,Pa=Array.isArray,Qa=pa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ra={pending:!1,data:null,method:null,action:null},hb=qa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,ob={prefetchDNS:ib,preconnect:jb,preload:kb,preloadModule:lb,preinit:mb,preinitModule:nb},pb=w('"></template>'),wb=w("<script>"),xb=w("\x3c/script>"),yb=w('<script src="'),zb=w('<script type="module" src="'),Ab=w('" nonce="'),Bb=w('" integrity="'),Cb=w('" crossorigin="'),Db=w('" async="">\x3c/script>'),
Eb=/(<\/|<)(s)(cript)/gi;function Fb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}
function Gb(a,b,c,d,e,g,f){b=void 0===b?"":b;var l=void 0===c?wb:w('<script nonce="'+y(c)+'">'),m=[],q=null,t=0;void 0!==d&&m.push(l,(""+d).replace(Eb,Fb),xb);void 0!==f&&(t=1,"string"===typeof f?(q={src:f,chunks:[]},Hb(q.chunks,{src:f,async:!0,integrity:void 0,nonce:c})):(q={src:f.src,chunks:[]},Hb(q.chunks,{src:f.src,async:!0,integrity:f.integrity,nonce:c})));if(void 0!==e)for(d=0;d<e.length;d++){var n=e[d];f="string"===typeof n?n:n.src;var u="string"===typeof n?void 0:n.integrity;n="string"===
typeof n||null==n.crossOrigin?void 0:"use-credentials"===n.crossOrigin?"use-credentials":"";var H=a,I={rel:"preload",href:f,as:"script",fetchPriority:"low",nonce:c,integrity:u,crossOrigin:n},E={type:"preload",chunks:[],state:0,props:I};H.preloadsMap.set("[script]"+f,E);H.bootstrapScripts.add(E);z(E.chunks,I);m.push(yb,y(f));c&&m.push(Ab,y(c));u&&m.push(Bb,y(u));"string"===typeof n&&m.push(Cb,y(n));m.push(Db)}if(void 0!==g)for(e=0;e<g.length;e++)u=g[e],d="string"===typeof u?u:u.src,f="string"===typeof u?
void 0:u.integrity,u="string"===typeof u||null==u.crossOrigin?void 0:"use-credentials"===u.crossOrigin?"use-credentials":"",n=a,H={rel:"modulepreload",href:d,fetchPriority:"low",nonce:c,integrity:f,crossOrigin:u},I={type:"preload",chunks:[],state:0,props:H},n.preloadsMap.set("[script]"+d,I),n.bootstrapScripts.add(I),z(I.chunks,H),m.push(zb,y(d)),c&&m.push(Ab,y(c)),f&&m.push(Bb,y(f)),"string"===typeof u&&m.push(Cb,y(u)),m.push(Db);return{bootstrapChunks:m,placeholderPrefix:w(b+"P:"),segmentPrefix:w(b+
"S:"),boundaryPrefix:b+"B:",idPrefix:b,nextSuspenseID:0,streamingFormat:t,startInlineScript:l,instructions:0,externalRuntimeScript:q,htmlChunks:null,headChunks:null,hasBody:!1,charsetChunks:[],preconnectChunks:[],preloadChunks:[],hoistableChunks:[],stylesToHoist:!1,nonce:c}}function A(a,b,c){return{insertionMode:a,selectedValue:b,noscriptTagInScope:c}}function Ib(a){return A("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,!1)}
function Jb(a,b,c){switch(b){case "noscript":return A(2,null,!0);case "select":return A(2,null!=c.value?c.value:c.defaultValue,a.noscriptTagInScope);case "svg":return A(3,null,a.noscriptTagInScope);case "math":return A(4,null,a.noscriptTagInScope);case "foreignObject":return A(2,null,a.noscriptTagInScope);case "table":return A(5,null,a.noscriptTagInScope);case "thead":case "tbody":case "tfoot":return A(6,null,a.noscriptTagInScope);case "colgroup":return A(8,null,a.noscriptTagInScope);case "tr":return A(7,
null,a.noscriptTagInScope)}return 5<=a.insertionMode?A(2,null,a.noscriptTagInScope):0===a.insertionMode?"html"===b?A(1,null,!1):A(2,null,!1):1===a.insertionMode?A(2,null,!1):a}var Kb=w("\x3c!-- --\x3e");function Lb(a,b,c,d){if(""===b)return d;d&&a.push(Kb);a.push(y(b));return!0}var Mb=new Map,Nb=w(' style="'),Ob=w(":"),Pb=w(";");
function Qb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(x.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var g=y(d);e=y((""+e).trim())}else g=Mb.get(d),void 0===g&&(g=w(y(d.replace(Na,"-$1").toLowerCase().replace(Oa,"-ms-"))),Mb.set(d,g)),e="number"===typeof e?0===e||Ka.has(d)?""+e:e+"px":
y((""+e).trim());c?(c=!1,a.push(Nb,g,Ob,e)):a.push(Pb,g,Ob,e)}}c||a.push(F)}var G=w(" "),J=w('="'),F=w('"'),Rb=w('=""');function Sb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(G,b,Rb)}function K(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(G,b,J,y(c),F)}function Tb(a){var b=a.nextSuspenseID++;return a.idPrefix+b}var Ub=w(y("javascript:throw new Error('A React form was unexpectedly submitted.')")),Vb=w('<input type="hidden"');
function Wb(a,b){this.push(Vb);if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");K(this,"name",b);K(this,"value",a);this.push(cc)}
function dc(a,b,c,d,e,g,f){var l=null;"function"===typeof c&&("function"===typeof c.$$FORM_ACTION?(d=Tb(b),b=c.$$FORM_ACTION(d),f=b.name,c=b.action||"",d=b.encType,e=b.method,g=b.target,l=b.data):(a.push(G,"formAction",J,Ub,F),g=e=d=c=f=null,ec(b)));null!=f&&M(a,"name",f);null!=c&&M(a,"formAction",c);null!=d&&M(a,"formEncType",d);null!=e&&M(a,"formMethod",e);null!=g&&M(a,"formTarget",g);return l}
function M(a,b,c){switch(b){case "className":K(a,"class",c);break;case "tabIndex":K(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":K(a,b,c);break;case "style":Qb(a,c);break;case "src":case "href":if(""===c)break;case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(G,b,J,y(""+c),F);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Sb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(G,"xlink:href",J,y(""+c),F);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(G,b,J,y(c),F);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(G,b,Rb);break;case "capture":case "download":!0===c?a.push(G,b,Rb):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(G,b,J,y(c),F);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(G,b,J,y(c),F);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(G,b,J,y(c),F);break;case "xlinkActuate":K(a,"xlink:actuate",c);break;case "xlinkArcrole":K(a,
"xlink:arcrole",c);break;case "xlinkRole":K(a,"xlink:role",c);break;case "xlinkShow":K(a,"xlink:show",c);break;case "xlinkTitle":K(a,"xlink:title",c);break;case "xlinkType":K(a,"xlink:type",c);break;case "xmlBase":K(a,"xml:base",c);break;case "xmlLang":K(a,"xml:lang",c);break;case "xmlSpace":K(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=La.get(b)||b,Ea(b)){switch(typeof c){case "function":case "symbol":return;case "boolean":var d=b.toLowerCase().slice(0,
5);if("data-"!==d&&"aria-"!==d)return}a.push(G,b,J,y(c),F)}}}var N=w(">"),cc=w("/>");function S(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function fc(a){var b="";pa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var gc=w(' selected=""'),hc=w('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});');
function ec(a){0!==(a.instructions&16)||a.externalRuntimeScript||(a.instructions|=16,a.bootstrapChunks.unshift(a.startInlineScript,hc,xb))}
function ic(a,b,c,d,e,g,f){var l=b.rel,m=b.href,q=b.precedence;if(3===g||f||null!=b.itemProp||"string"!==typeof l||"string"!==typeof m||""===m)return z(a,b),null;if("stylesheet"===b.rel){c="[style]"+m;if("string"!==typeof q||null!=b.disabled||b.onLoad||b.onError)return z(a,b);g=d.stylesMap.get(c);g||(b=Aa({},b,{"data-precedence":b.precedence,precedence:null}),g=d.preloadsMap.get(c),f=0,g&&(g.state|=4,l=g.props,null==b.crossOrigin&&(b.crossOrigin=l.crossOrigin),null==b.integrity&&(b.integrity=l.integrity),
g.state&3&&(f=8)),g={type:"stylesheet",chunks:[],state:f,props:b},d.stylesMap.set(c,g),b=d.precedences.get(q),b||(b=new Set,d.precedences.set(q,b),c={type:"style",chunks:[],state:0,props:{precedence:q,hrefs:[]}},b.add(c),d.stylePrecedences.set(q,c)),b.add(g));d.boundaryResources&&d.boundaryResources.add(g);e&&a.push(Kb);return null}if(b.onLoad||b.onError)return z(a,b);e&&a.push(Kb);switch(b.rel){case "preconnect":case "dns-prefetch":return z(c.preconnectChunks,b);case "preload":return z(c.preloadChunks,
b);default:return z(c.hoistableChunks,b)}}function z(a,b){a.push(U("link"));for(var c in b)if(x.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:M(a,c,d)}}a.push(cc);return null}function jc(a,b,c){var d="";"string"===typeof b&&""!==b?(d+="["+b+"]","string"===typeof c&&(d+="["+c+"]")):d+="[][]"+a;return"[image]"+d}
function kc(a,b,c){a.push(U(c));for(var d in b)if(x.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:M(a,d,e)}}a.push(cc);return null}
function lc(a,b){a.push(U("title"));var c=null,d=null,e;for(e in b)if(x.call(b,e)){var g=b[e];if(null!=g)switch(e){case "children":c=g;break;case "dangerouslySetInnerHTML":d=g;break;default:M(a,e,g)}}a.push(N);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(y(""+b));S(a,d,c);a.push(mc,"title",nc);return null}
function Hb(a,b){a.push(U("script"));var c=null,d=null,e;for(e in b)if(x.call(b,e)){var g=b[e];if(null!=g)switch(e){case "children":c=g;break;case "dangerouslySetInnerHTML":d=g;break;default:M(a,e,g)}}a.push(N);S(a,d,c);"string"===typeof c&&a.push(y(c));a.push(mc,"script",nc);return null}
function oc(a,b,c){a.push(U(c));var d=c=null,e;for(e in b)if(x.call(b,e)){var g=b[e];if(null!=g)switch(e){case "children":c=g;break;case "dangerouslySetInnerHTML":d=g;break;default:M(a,e,g)}}a.push(N);S(a,d,c);return"string"===typeof c?(a.push(y(c)),null):c}var pc=w("\n"),qc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,rc=new Map;function U(a){var b=rc.get(a);if(void 0===b){if(!qc.test(a))throw Error("Invalid tag: "+a);b=w("<"+a);rc.set(a,b)}return b}var sc=w("<!DOCTYPE html>");
function tc(a,b,c,d,e,g,f){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(U("select"));var l=null,m=null,q;for(q in c)if(x.call(c,q)){var t=c[q];if(null!=t)switch(q){case "children":l=t;break;case "dangerouslySetInnerHTML":m=t;break;case "defaultValue":case "value":break;default:M(a,q,t)}}a.push(N);S(a,m,l);return l;case "option":var n=g.selectedValue;a.push(U("option"));var u=null,H=null,I=null,E=null,r;for(r in c)if(x.call(c,
r)){var O=c[r];if(null!=O)switch(r){case "children":u=O;break;case "selected":I=O;break;case "dangerouslySetInnerHTML":E=O;break;case "value":H=O;default:M(a,r,O)}}if(null!=n){var P=null!==H?""+H:fc(u);if(Pa(n))for(var L=0;L<n.length;L++){if(""+n[L]===P){a.push(gc);break}}else""+n===P&&a.push(gc)}else I&&a.push(gc);a.push(N);S(a,E,u);return u;case "textarea":a.push(U("textarea"));var B=null,Q=null,C=null,D;for(D in c)if(x.call(c,D)){var ha=c[D];if(null!=ha)switch(D){case "children":C=ha;break;case "value":B=
ha;break;case "defaultValue":Q=ha;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:M(a,D,ha)}}null===B&&null!==Q&&(B=Q);a.push(N);if(null!=C){if(null!=B)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Pa(C)&&1<C.length)throw Error("<textarea> can only have at most one child.");B=""+C}"string"===typeof B&&"\n"===B[0]&&a.push(pc);null!==B&&a.push(y(""+B));return null;case "input":a.push(U("input"));
var Fa=null,ua=null,ia=null,ca=null,va=null,ja=null,ka=null,la=null,Ga=null,Z;for(Z in c)if(x.call(c,Z)){var X=c[Z];if(null!=X)switch(Z){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":Fa=X;break;case "formAction":ua=X;break;case "formEncType":ia=X;break;case "formMethod":ca=X;break;case "formTarget":va=X;break;case "defaultChecked":Ga=X;break;case "defaultValue":ka=X;break;case "checked":la=
X;break;case "value":ja=X;break;default:M(a,Z,X)}}var Pc=dc(a,e,ua,ia,ca,va,Fa);null!==la?Sb(a,"checked",la):null!==Ga&&Sb(a,"checked",Ga);null!==ja?M(a,"value",ja):null!==ka&&M(a,"value",ka);a.push(cc);null!==Pc&&Pc.forEach(Wb,a);return null;case "button":a.push(U("button"));var Sa=null,Qc=null,Rc=null,Sc=null,Tc=null,Uc=null,Vc=null,Ta;for(Ta in c)if(x.call(c,Ta)){var da=c[Ta];if(null!=da)switch(Ta){case "children":Sa=da;break;case "dangerouslySetInnerHTML":Qc=da;break;case "name":Rc=da;break;case "formAction":Sc=
da;break;case "formEncType":Tc=da;break;case "formMethod":Uc=da;break;case "formTarget":Vc=da;break;default:M(a,Ta,da)}}var Wc=dc(a,e,Sc,Tc,Uc,Vc,Rc);a.push(N);null!==Wc&&Wc.forEach(Wb,a);S(a,Qc,Sa);if("string"===typeof Sa){a.push(y(Sa));var Xc=null}else Xc=Sa;return Xc;case "form":a.push(U("form"));var Ua=null,Yc=null,ma=null,Va=null,Wa=null,Xa=null,Ya;for(Ya in c)if(x.call(c,Ya)){var na=c[Ya];if(null!=na)switch(Ya){case "children":Ua=na;break;case "dangerouslySetInnerHTML":Yc=na;break;case "action":ma=
na;break;case "encType":Va=na;break;case "method":Wa=na;break;case "target":Xa=na;break;default:M(a,Ya,na)}}var Xb=null,Yb=null;if("function"===typeof ma)if("function"===typeof ma.$$FORM_ACTION){var Re=Tb(e),Ha=ma.$$FORM_ACTION(Re);ma=Ha.action||"";Va=Ha.encType;Wa=Ha.method;Xa=Ha.target;Xb=Ha.data;Yb=Ha.name}else a.push(G,"action",J,Ub,F),Xa=Wa=Va=ma=null,ec(e);null!=ma&&M(a,"action",ma);null!=Va&&M(a,"encType",Va);null!=Wa&&M(a,"method",Wa);null!=Xa&&M(a,"target",Xa);a.push(N);null!==Yb&&(a.push(Vb),
K(a,"name",Yb),a.push(cc),null!==Xb&&Xb.forEach(Wb,a));S(a,Yc,Ua);if("string"===typeof Ua){a.push(y(Ua));var Zc=null}else Zc=Ua;return Zc;case "menuitem":a.push(U("menuitem"));for(var qb in c)if(x.call(c,qb)){var $c=c[qb];if(null!=$c)switch(qb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:M(a,qb,$c)}}a.push(N);return null;case "title":if(3===g.insertionMode||g.noscriptTagInScope||null!=c.itemProp)var ad=lc(a,
c);else lc(e.hoistableChunks,c),ad=null;return ad;case "link":return ic(a,c,e,d,f,g.insertionMode,g.noscriptTagInScope);case "script":var Zb=c.async;if("string"!==typeof c.src||!c.src||!Zb||"function"===typeof Zb||"symbol"===typeof Zb||c.onLoad||c.onError||3===g.insertionMode||g.noscriptTagInScope||null!=c.itemProp)var bd=Hb(a,c);else{var $b="[script]"+c.src,Za=d.scriptsMap.get($b);if(!Za){Za={type:"script",chunks:[],state:0,props:null};d.scriptsMap.set($b,Za);d.scripts.add(Za);var cd=c,rb=d.preloadsMap.get($b);
if(rb){rb.state|=4;var sb=cd=Aa({},c),dd=rb.props;null==sb.crossOrigin&&(sb.crossOrigin=dd.crossOrigin);null==sb.integrity&&(sb.integrity=dd.integrity)}Hb(Za.chunks,cd)}f&&a.push(Kb);bd=null}return bd;case "style":var $a=c.precedence,ab=c.href;if(3===g.insertionMode||g.noscriptTagInScope||null!=c.itemProp||"string"!==typeof $a||"string"!==typeof ab||""===ab){a.push(U("style"));var Ia=null,ed=null,bb;for(bb in c)if(x.call(c,bb)){var tb=c[bb];if(null!=tb)switch(bb){case "children":Ia=tb;break;case "dangerouslySetInnerHTML":ed=
tb;break;default:M(a,bb,tb)}}a.push(N);var cb=Array.isArray(Ia)?2>Ia.length?Ia[0]:null:Ia;"function"!==typeof cb&&"symbol"!==typeof cb&&null!==cb&&void 0!==cb&&a.push(y(""+cb));S(a,ed,Ia);a.push(mc,"style",nc);var fd=null}else{var gd="[style]"+ab,ea=d.stylesMap.get(gd);if(!ea){if(ea=d.stylePrecedences.get($a))ea.props.hrefs.push(ab);else{ea={type:"style",chunks:[],state:0,props:{precedence:$a,hrefs:[ab]}};d.stylePrecedences.set($a,ea);var hd=new Set;hd.add(ea);d.precedences.set($a,hd)}d.stylesMap.set(gd,
ea);d.boundaryResources&&d.boundaryResources.add(ea);var id=ea.chunks,Ja=null,jd=null,ub;for(ub in c)if(x.call(c,ub)){var ac=c[ub];if(null!=ac)switch(ub){case "children":Ja=ac;break;case "dangerouslySetInnerHTML":jd=ac}}var db=Array.isArray(Ja)?2>Ja.length?Ja[0]:null:Ja;"function"!==typeof db&&"symbol"!==typeof db&&null!==db&&void 0!==db&&id.push(y(""+db));S(id,jd,Ja)}f&&a.push(Kb);fd=void 0}return fd;case "meta":if(3===g.insertionMode||g.noscriptTagInScope||null!=c.itemProp)var kd=kc(a,c,"meta");
else f&&a.push(Kb),kd="string"===typeof c.charSet?kc(e.charsetChunks,c,"meta"):"viewport"===c.name?kc(e.preconnectChunks,c,"meta"):kc(e.hoistableChunks,c,"meta");return kd;case "listing":case "pre":a.push(U(b));var eb=null,fb=null,gb;for(gb in c)if(x.call(c,gb)){var vb=c[gb];if(null!=vb)switch(gb){case "children":eb=vb;break;case "dangerouslySetInnerHTML":fb=vb;break;default:M(a,gb,vb)}}a.push(N);if(null!=fb){if(null!=eb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");
if("object"!==typeof fb||!("__html"in fb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var wa=fb.__html;null!==wa&&void 0!==wa&&("string"===typeof wa&&0<wa.length&&"\n"===wa[0]?a.push(pc,wa):a.push(""+wa))}"string"===typeof eb&&"\n"===eb[0]&&a.push(pc);return eb;case "img":var T=c.src,R=c.srcSet;if("lazy"!==c.loading&&("string"===typeof T||"string"===typeof R)&&"low"!==c.fetchPriority&&
("string"!==typeof T||":"!==T[4]||"d"!==T[0]&&"D"!==T[0]||"a"!==T[1]&&"A"!==T[1]||"t"!==T[2]&&"T"!==T[2]||"a"!==T[3]&&"A"!==T[3])&&("string"!==typeof R||":"!==R[4]||"d"!==R[0]&&"D"!==R[0]||"a"!==R[1]&&"A"!==R[1]||"t"!==R[2]&&"T"!==R[2]||"a"!==R[3]&&"A"!==R[3])){var ld=c.sizes,md=jc(T,R,ld),xa=d.preloadsMap.get(md);xa||(xa={type:"preload",chunks:[],state:0,props:{rel:"preload",as:"image",href:R?void 0:T,imageSrcSet:R,imageSizes:ld,crossOrigin:c.crossOrigin,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,
referrerPolicy:c.referrerPolicy}},d.preloadsMap.set(md,xa),z(xa.chunks,xa.props));"high"===c.fetchPriority||10>d.highImagePreloads.size?d.highImagePreloads.add(xa):d.bulkPreloads.add(xa)}return kc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return kc(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;
case "head":if(2>g.insertionMode&&null===e.headChunks){e.headChunks=[];var nd=oc(e.headChunks,c,"head")}else nd=oc(a,c,"head");return nd;case "html":if(0===g.insertionMode&&null===e.htmlChunks){e.htmlChunks=[sc];var od=oc(e.htmlChunks,c,"html")}else od=oc(a,c,"html");return od;default:if(-1!==b.indexOf("-")){a.push(U(b));var bc=null,pd=null,oa;for(oa in c)if(x.call(c,oa)){var V=c[oa];if(null!=V&&"function"!==typeof V&&"object"!==typeof V&&!1!==V)switch(!0===V&&(V=""),"className"===oa&&(oa="class"),
oa){case "children":bc=V;break;case "dangerouslySetInnerHTML":pd=V;break;case "style":Qb(a,V);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:Ea(oa)&&"function"!==typeof V&&"symbol"!==typeof V&&a.push(G,oa,J,y(V),F)}}a.push(N);S(a,pd,bc);return bc}}return oc(a,c,b)}var mc=w("</"),nc=w(">");function uc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)p(a,b[c]);return c<b.length?(c=b[c],b.length=0,v(a,c)):!0}
var vc=w('<template id="'),wc=w('"></template>'),xc=w("\x3c!--$--\x3e"),yc=w('\x3c!--$?--\x3e<template id="'),zc=w('"></template>'),Ac=w("\x3c!--$!--\x3e"),Bc=w("\x3c!--/$--\x3e"),Cc=w("<template"),Dc=w('"'),Ec=w(' data-dgst="');w(' data-msg="');w(' data-stck="');var Fc=w("></template>");function Gc(a,b,c){p(a,yc);if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");p(a,c);return v(a,zc)}
var Hc=w('<div hidden id="'),Ic=w('">'),Jc=w("</div>"),Kc=w('<svg aria-hidden="true" style="display:none" id="'),Lc=w('">'),Mc=w("</svg>"),Nc=w('<math aria-hidden="true" style="display:none" id="'),Oc=w('">'),qd=w("</math>"),rd=w('<table hidden id="'),sd=w('">'),td=w("</table>"),ud=w('<table hidden><tbody id="'),vd=w('">'),wd=w("</tbody></table>"),xd=w('<table hidden><tr id="'),yd=w('">'),zd=w("</tr></table>"),Ad=w('<table hidden><colgroup id="'),Bd=w('">'),Cd=w("</colgroup></table>");
function Dd(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return p(a,Hc),p(a,b.segmentPrefix),p(a,d.toString(16)),v(a,Ic);case 3:return p(a,Kc),p(a,b.segmentPrefix),p(a,d.toString(16)),v(a,Lc);case 4:return p(a,Nc),p(a,b.segmentPrefix),p(a,d.toString(16)),v(a,Oc);case 5:return p(a,rd),p(a,b.segmentPrefix),p(a,d.toString(16)),v(a,sd);case 6:return p(a,ud),p(a,b.segmentPrefix),p(a,d.toString(16)),v(a,vd);case 7:return p(a,xd),p(a,b.segmentPrefix),p(a,d.toString(16)),v(a,yd);case 8:return p(a,
Ad),p(a,b.segmentPrefix),p(a,d.toString(16)),v(a,Bd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}function Ed(a,b){switch(b.insertionMode){case 0:case 1:case 2:return v(a,Jc);case 3:return v(a,Mc);case 4:return v(a,qd);case 5:return v(a,td);case 6:return v(a,wd);case 7:return v(a,zd);case 8:return v(a,Cd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}
var Fd=w('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};;$RS("'),Gd=w('$RS("'),Hd=w('","'),Id=w('")\x3c/script>'),Jd=w('<template data-rsi="" data-sid="'),Kd=w('" data-pid="'),Ld=w('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
Md=w('$RC("'),Nd=w('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Od=w('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Pd=w('$RR("'),Qd=w('","'),Rd=w('",'),Sd=w('"'),Td=w(")\x3c/script>"),Ud=w('<template data-rci="" data-bid="'),Vd=w('<template data-rri="" data-bid="'),Wd=w('" data-sid="'),Xd=w('" data-sty="'),Yd=w('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),Zd=w('$RX("'),$d=w('"'),ae=w(","),be=w(")\x3c/script>"),ce=w('<template data-rxi="" data-bid="'),de=w('" data-dgst="'),
ee=w('" data-msg="'),fe=w('" data-stck="'),ge=/[<\u2028\u2029]/g;function he(a){return JSON.stringify(a).replace(ge,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var ie=/[&><\u2028\u2029]/g;
function je(a){return JSON.stringify(a).replace(ie,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var ke=w('<style media="not all" data-precedence="'),le=w('" data-href="'),me=w('">'),ne=w("</style>"),oe=!1,pe=!0;function qe(a){if("stylesheet"===a.type&&0===(a.state&1))oe=!0;else if("style"===a.type){var b=a.chunks,c=a.props.hrefs,d=0;if(b.length){p(this,ke);p(this,y(a.props.precedence));if(c.length){for(p(this,le);d<c.length-1;d++)p(this,y(c[d])),p(this,re);p(this,y(c[d]))}p(this,me);for(d=0;d<b.length;d++)p(this,b[d]);pe=v(this,ne);oe=!0;b.length=0;c.length=0}}}
function se(a,b,c){oe=!1;pe=!0;b.forEach(qe,a);oe&&(c.stylesToHoist=!0);return pe}function te(a){if(0===(a.state&7)){for(var b=a.chunks,c=0;c<b.length;c++)p(this,b[c]);a.state|=1}}function ue(a){if(0===(a.state&7)){for(var b=a.chunks,c=0;c<b.length;c++)p(this,b[c]);a.state|=2}}var ve=null,we=!1;function xe(a,b,c){b=a.chunks;if(a.state&3)c.delete(a);else if("style"===a.type)ve=a;else{z(b,a.props);for(c=0;c<b.length;c++)p(this,b[c]);a.state|=1;we=!0}}
var ye=w('<style data-precedence="'),ze=w('" data-href="'),re=w(" "),Ae=w('">'),Be=w("</style>");function Ce(a,b){we=!1;a.forEach(xe,this);a.clear();a=ve.chunks;var c=ve.props.hrefs;if(!1===we||a.length){p(this,ye);p(this,y(b));b=0;if(c.length){for(p(this,ze);b<c.length-1;b++)p(this,y(c[b])),p(this,re);p(this,y(c[b]))}p(this,Ae);for(b=0;b<a.length;b++)p(this,a[b]);p(this,Be);a.length=0;c.length=0}}
function De(a){if(!(a.state&8)&&"style"!==a.type){var b=a.chunks,c=a.props;z(b,{rel:"preload",as:"style",href:a.props.href,crossOrigin:c.crossOrigin,fetchPriority:c.fetchPriority,integrity:c.integrity,media:c.media,hrefLang:c.hrefLang,referrerPolicy:c.referrerPolicy});for(c=0;c<b.length;c++)p(this,b[c]);a.state|=8;b.length=0}}function Ee(a){a.forEach(De,this);a.clear()}var Fe=w("["),Ge=w(",["),He=w(","),Ie=w("]");
function Je(a,b){p(a,Fe);var c=Fe;b.forEach(function(d){if("style"!==d.type&&!(d.state&1))if(d.state&3)p(a,c),p(a,je(""+d.props.href)),p(a,Ie),c=Ge;else if("stylesheet"===d.type){p(a,c);var e=d.props["data-precedence"],g=d.props;p(a,je(""+d.props.href));e=""+e;p(a,He);p(a,je(e));for(var f in g)if(x.call(g,f)){var l=g[f];if(null!=l)switch(f){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var m=f.toLowerCase();switch(typeof l){case "function":case "symbol":break a}switch(f){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":m="class";l=""+l;break;case "hidden":if(!1===l)break a;l="";break;case "src":case "href":l=""+l;break;default:if(2<f.length&&("o"===f[0]||"O"===f[0])&&("n"===f[1]||"N"===f[1])||!Ea(f))break a;l=""+l}p(e,He);p(e,je(m));p(e,He);p(e,je(l))}}}p(a,
Ie);c=Ge;d.state|=2}});p(a,Ie)}
function Ke(a,b){p(a,Fe);var c=Fe;b.forEach(function(d){if("style"!==d.type&&!(d.state&1))if(d.state&3)p(a,c),p(a,y(JSON.stringify(""+d.props.href))),p(a,Ie),c=Ge;else if("stylesheet"===d.type){p(a,c);var e=d.props["data-precedence"],g=d.props;p(a,y(JSON.stringify(""+d.props.href)));e=""+e;p(a,He);p(a,y(JSON.stringify(e)));for(var f in g)if(x.call(g,f)){var l=g[f];if(null!=l)switch(f){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:a:{e=
a;var m=f.toLowerCase();switch(typeof l){case "function":case "symbol":break a}switch(f){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":m="class";l=""+l;break;case "hidden":if(!1===l)break a;l="";break;case "src":case "href":l=""+l;break;default:if(2<f.length&&("o"===f[0]||"O"===f[0])&&("n"===f[1]||"N"===f[1])||!Ea(f))break a;l=""+l}p(e,He);p(e,y(JSON.stringify(m)));p(e,He);p(e,y(JSON.stringify(l)))}}}p(a,
Ie);c=Ge;d.state|=2}});p(a,Ie)}function ib(a){var b=Le();if(b){var c=b.resources;if("string"===typeof a&&a){var d="[prefetchDNS]"+a,e=c.preconnectsMap.get(d);e||(e={type:"preconnect",chunks:[],state:0,props:null},c.preconnectsMap.set(d,e),z(e.chunks,{href:a,rel:"dns-prefetch"}));c.preconnects.add(e);Me(b)}}}
function jb(a,b){var c=Le();if(c){var d=c.resources;if("string"===typeof a&&a){b=null==b||"string"!==typeof b.crossOrigin?null:"use-credentials"===b.crossOrigin?"use-credentials":"";var e="[preconnect]["+(null===b?"null":b)+"]"+a,g=d.preconnectsMap.get(e);g||(g={type:"preconnect",chunks:[],state:0,props:null},d.preconnectsMap.set(e,g),z(g.chunks,{rel:"preconnect",href:a,crossOrigin:b}));d.preconnects.add(g);Me(c)}}}
function kb(a,b){var c=Le();if(c){var d=c.resources;if("string"===typeof a&&a&&"object"===typeof b&&null!==b&&"string"===typeof b.as&&b.as){var e=b.as;var g="image"===e?jc(a,b.imageSrcSet,b.imageSizes):"["+e+"]"+a;var f=d.preloadsMap.get(g);f||(f={type:"preload",chunks:[],state:0,props:{rel:"preload",as:e,href:"image"===e&&b.imageSrcSet?void 0:a,crossOrigin:"font"===e?"":b.crossOrigin,integrity:b.integrity,type:b.type,nonce:b.nonce,fetchPriority:b.fetchPriority,imageSrcSet:b.imageSrcSet,imageSizes:b.imageSizes,
referrerPolicy:b.referrerPolicy}},d.preloadsMap.set(g,f),z(f.chunks,f.props));"font"===e?d.fontPreloads.add(f):"image"===e&&"high"===b.fetchPriority?d.highImagePreloads.add(f):d.bulkPreloads.add(f);Me(c)}}}
function lb(a,b){var c=Le();if(c){var d=c.resources;if("string"===typeof a&&a){var e=b&&"string"===typeof b.as?b.as:"script",g="["+e+"]"+a,f=d.preloadsMap.get(g);f||(f={type:"preload",chunks:[],state:0,props:{rel:"modulepreload",as:"script"!==e?e:void 0,href:a,crossOrigin:b?b.crossOrigin:void 0,integrity:b?b.integrity:void 0}},d.preloadsMap.set(g,f),z(f.chunks,f.props));d.bulkPreloads.add(f);Me(c)}}}
function mb(a,b){var c=Le();if(c){var d=c.resources;if("string"===typeof a&&a&&"object"===typeof b&&null!==b){var e=b.as;switch(e){case "style":var g="["+e+"]"+a,f=d.stylesMap.get(g);e=b.precedence||"default";if(!f){f=0;var l=d.preloadsMap.get(g);l&&l.state&3&&(f=8);f={type:"stylesheet",chunks:[],state:f,props:{rel:"stylesheet",href:a,"data-precedence":e,crossOrigin:b.crossOrigin,integrity:b.integrity,fetchPriority:b.fetchPriority}};d.stylesMap.set(g,f);a=d.precedences.get(e);a||(a=new Set,d.precedences.set(e,
a),b={type:"style",chunks:[],state:0,props:{precedence:e,hrefs:[]}},a.add(b),d.stylePrecedences.set(e,b));a.add(f);Me(c)}break;case "script":g="["+e+"]"+a,e=d.scriptsMap.get(g),e||(e={type:"script",chunks:[],state:0,props:null},d.scriptsMap.set(g,e),a={src:a,async:!0,crossOrigin:b.crossOrigin,integrity:b.integrity,nonce:b.nonce,fetchPriority:b.fetchPriority},d.scripts.add(e),Hb(e.chunks,a),Me(c))}}}}
function nb(a,b){var c=Le();if(c){var d=c.resources;if("string"===typeof a&&a){var e=b&&"string"===typeof b.as?b.as:"script";switch(e){case "script":var g="["+e+"]"+a;e=d.scriptsMap.get(g);e||(e={type:"script",chunks:[],state:0,props:null},d.scriptsMap.set(g,e),a={src:a,type:"module",async:!0,crossOrigin:b?b.crossOrigin:void 0,integrity:b?b.integrity:void 0},d.scripts.add(e),Hb(e.chunks,a),Me(c))}}}}function Ne(a){this.add(a)}
var Oe=new fa.AsyncLocalStorage,Pe=Symbol.for("react.element"),Qe=Symbol.for("react.portal"),Se=Symbol.for("react.fragment"),Te=Symbol.for("react.strict_mode"),Ue=Symbol.for("react.profiler"),Ve=Symbol.for("react.provider"),We=Symbol.for("react.context"),Xe=Symbol.for("react.server_context"),Ye=Symbol.for("react.forward_ref"),Ze=Symbol.for("react.suspense"),$e=Symbol.for("react.suspense_list"),af=Symbol.for("react.memo"),bf=Symbol.for("react.lazy"),cf=Symbol.for("react.scope"),df=Symbol.for("react.debug_trace_mode"),
ef=Symbol.for("react.offscreen"),ff=Symbol.for("react.legacy_hidden"),gf=Symbol.for("react.cache"),hf=Symbol.for("react.default_value"),jf=Symbol.for("react.memo_cache_sentinel"),kf=Symbol.for("react.postpone"),lf=Symbol.iterator;function mf(a){if(null===a||"object"!==typeof a)return null;a=lf&&a[lf]||a["@@iterator"];return"function"===typeof a?a:null}
function nf(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Se:return"Fragment";case Qe:return"Portal";case Ue:return"Profiler";case Te:return"StrictMode";case Ze:return"Suspense";case $e:return"SuspenseList";case gf:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case We:return(a.displayName||"Context")+".Consumer";case Ve:return(a._context.displayName||"Context")+".Provider";case Ye:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case af:return b=a.displayName||null,null!==b?b:nf(a.type)||"Memo";case bf:b=a._payload;a=a._init;try{return nf(a(b))}catch(c){break}case Xe:return(a.displayName||a._globalName)+".Provider"}return null}var of={};function pf(a,b){a=a.contextTypes;if(!a)return of;var c={},d;for(d in a)c[d]=b[d];return c}var qf=null;
function rf(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");rf(a,c)}b.context._currentValue=b.value}}function sf(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&sf(a)}
function tf(a){var b=a.parent;null!==b&&tf(b);a.context._currentValue=a.value}function uf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?rf(a,b):uf(a,b)}
function vf(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?rf(a,c):vf(a,c);b.context._currentValue=b.value}function wf(a){var b=qf;b!==a&&(null===b?tf(a):null===a?sf(b):b.depth===a.depth?rf(b,a):b.depth>a.depth?uf(b,a):vf(b,a),qf=a)}
var xf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function yf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=xf;a.props=c;a.state=e;var g={queue:[],replace:!1};a._reactInternals=g;var f=b.contextType;a.context="object"===typeof f&&null!==f?f._currentValue:d;f=b.getDerivedStateFromProps;"function"===typeof f&&(f=f(c,e),e=null===f||void 0===f?e:Aa({},e,f),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&xf.enqueueReplaceState(a,a.state,null),null!==g.queue&&0<g.queue.length)if(b=g.queue,f=g.replace,g.queue=null,g.replace=!1,f&&1===b.length)a.state=b[0];else{g=f?b[0]:a.state;e=!0;for(f=f?1:0;f<b.length;f++){var l=b[f];l="function"===typeof l?l.call(a,g,c,d):l;null!=l&&(e?(e=!1,g=Aa({},g,l)):Aa(g,l))}a.state=g}else g.queue=null}
var zf={id:1,overflow:""};function Af(a,b,c){var d=a.id;a=a.overflow;var e=32-Bf(d)-1;d&=~(1<<e);c+=1;var g=32-Bf(b)+e;if(30<g){var f=e-e%5;g=(d&(1<<f)-1).toString(32);d>>=f;e-=f;return{id:1<<32-Bf(b)+e|c<<e|d,overflow:g+a}}return{id:1<<g|c<<e|d,overflow:a}}var Bf=Math.clz32?Math.clz32:Cf,Df=Math.log,Ef=Math.LN2;function Cf(a){a>>>=0;return 0===a?32:31-(Df(a)/Ef|0)|0}var Ff=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Gf(){}function Hf(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Gf,Gf),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}If=b;throw Ff;}}var If=null;
function Jf(){if(null===If)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=If;If=null;return a}function Kf(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Lf="function"===typeof Object.is?Object.is:Kf,Mf=null,Nf=null,Of=null,W=null,Pf=!1,Qf=!1,Rf=0,Sf=0,Tf=null,Uf=null,Vf=0;
function Wf(){if(null===Mf)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return Mf}
function Xf(){if(0<Vf)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function Yf(){null===W?null===Of?(Pf=!1,Of=W=Xf()):(Pf=!0,W=Of):null===W.next?(Pf=!1,W=W.next=Xf()):(Pf=!0,W=W.next);return W}function Zf(a,b,c,d){for(;Qf;)Qf=!1,Sf=Rf=0,Vf+=1,W=null,c=a(b,d);$f();return c}function ag(){var a=Tf;Tf=null;return a}function $f(){Nf=Mf=null;Qf=!1;Of=null;Vf=0;W=Uf=null}function bg(a,b){return"function"===typeof b?b(a):b}
function cg(a,b,c){Mf=Wf();W=Yf();if(Pf){var d=W.queue;b=d.dispatch;if(null!==Uf&&(c=Uf.get(d),void 0!==c)){Uf.delete(d);d=W.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);W.memoizedState=d;return[d,b]}return[W.memoizedState,b]}a=a===bg?"function"===typeof b?b():b:void 0!==c?c(b):b;W.memoizedState=a;a=W.queue={last:null,dispatch:null};a=a.dispatch=dg.bind(null,Mf,a);return[W.memoizedState,a]}
function eg(a,b){Mf=Wf();W=Yf();b=void 0===b?null:b;if(null!==W){var c=W.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Lf(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();W.memoizedState=[a,b];return a}
function dg(a,b,c){if(25<=Vf)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===Mf)if(Qf=!0,a={action:c,next:null},null===Uf&&(Uf=new Map),c=Uf.get(b),void 0===c)Uf.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function fg(){throw Error("A function wrapped in useEffectEvent can't be called during rendering.");}function gg(){throw Error("startTransition cannot be called during server rendering.");}
function hg(){throw Error("Cannot update optimistic state while rendering.");}function ig(a){var b=Sf;Sf+=1;null===Tf&&(Tf=[]);return Hf(Tf,a,b)}function jg(){throw Error("Cache cannot be refreshed during server rendering.");}function kg(){}
var mg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return ig(a);if(a.$$typeof===We||a.$$typeof===Xe)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){Wf();return a._currentValue},useMemo:eg,useReducer:cg,useRef:function(a){Mf=Wf();W=Yf();var b=W.memoizedState;return null===b?(a={current:a},W.memoizedState=a):b},useState:function(a){return cg(bg,a)},
useInsertionEffect:kg,useLayoutEffect:kg,useCallback:function(a,b){return eg(function(){return a},b)},useImperativeHandle:kg,useEffect:kg,useDebugValue:kg,useDeferredValue:function(a){Wf();return a},useTransition:function(){Wf();return[!1,gg]},useId:function(){var a=Nf.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Bf(a)-1)).toString(32)+b;var c=lg;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=Rf++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return jg},useEffectEvent:function(){return fg},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=jf;return b},useHostTransitionStatus:function(){Wf();return Ra},useOptimistic:function(a){Wf();return[a,hg]}},lg=null,ng={getCacheSignal:function(){throw Error("Not implemented.");
},getCacheForType:function(){throw Error("Not implemented.");}},og=Qa.ReactCurrentDispatcher,pg=Qa.ReactCurrentCache;function qg(a){console.error(a);return null}function rg(){}
function sg(a,b,c,d,e,g,f,l,m,q,t){hb.current=ob;var n=[],u=new Set;b={destination:null,flushScheduled:!1,responseState:c,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,resources:b,completedRootSegment:null,abortableTasks:u,pingedTasks:n,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],onError:void 0===g?qg:g,onPostpone:void 0===t?rg:t,onAllReady:void 0===f?rg:f,onShellReady:void 0===l?rg:l,onShellError:void 0===
m?rg:m,onFatalError:void 0===q?rg:q};d=tg(b,0,null,d,!1,!1);d.parentFlushed=!0;a=ug(b,null,a,null,d,u,null,of,null,zf);n.push(a);return b}var vg=null;function Le(){if(vg)return vg;var a=Oe.getStore();return a?a:null}function wg(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return xg(a)}))}
function ug(a,b,c,d,e,g,f,l,m,q){a.allPendingTasks++;null===d?a.pendingRootTasks++:d.pendingTasks++;var t={node:c,ping:function(){return wg(a,t)},blockedBoundary:d,blockedSegment:e,abortSet:g,keyPath:f,legacyContext:l,context:m,treeContext:q,thenableState:b};g.add(t);return t}function tg(a,b,c,d,e,g){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],formatContext:d,boundary:c,lastPushedText:e,textEmbedded:g}}
function yg(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function zg(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Ag(a,b,c,d){var e=c.render(),g=d.childContextTypes;if(null!==g&&void 0!==g){var f=b.legacyContext;if("function"!==typeof c.getChildContext)d=f;else{c=c.getChildContext();for(var l in c)if(!(l in g))throw Error((nf(d)||"Unknown")+'.getChildContext(): key "'+l+'" is not defined in childContextTypes.');d=Aa({},f,c)}b.legacyContext=d;Y(a,b,null,e,0);b.legacyContext=f}else Y(a,b,null,e,0)}
function Bg(a,b){if(a&&a.defaultProps){b=Aa({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Cg(a,b,c,d,e,g){if("function"===typeof d)if(d.prototype&&d.prototype.isReactComponent){var f=pf(d,b.legacyContext);c=d.contextType;c=new d(e,"object"===typeof c&&null!==c?c._currentValue:f);yf(c,d,e,f);Ag(a,b,c,d)}else if(f=pf(d,b.legacyContext),Mf={},Nf=b,Sf=Rf=0,Tf=c,c=d(e,f),c=Zf(d,e,c,f),g=0!==Rf,"object"===typeof c&&null!==c&&"function"===typeof c.render&&void 0===c.$$typeof)yf(c,d,e,f),Ag(a,b,c,d);else if(g){e=b.treeContext;b.treeContext=Af(e,1,0);try{Y(a,b,null,c,0)}finally{b.treeContext=
e}}else Y(a,b,null,c,0);else if("string"===typeof d){f=b.blockedSegment;g=tc(f.chunks,d,e,a.resources,a.responseState,f.formatContext,f.lastPushedText);f.lastPushedText=!1;c=f.formatContext;f.formatContext=Jb(c,d,e);Dg(a,b,g,0);f.formatContext=c;a:{b=f.chunks;switch(d){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=
c.insertionMode){a.responseState.hasBody=!0;break a}break;case "html":if(0===c.insertionMode)break a}b.push(mc,d,nc)}f.lastPushedText=!1}else{switch(d){case ff:case df:case Te:case Ue:case Se:Y(a,b,null,e.children,0);return;case ef:"hidden"!==e.mode&&Y(a,b,null,e.children,0);return;case $e:Y(a,b,null,e.children,0);return;case cf:throw Error("ReactDOMServer does not yet support scope components.");case Ze:a:{d=b.blockedBoundary;c=b.blockedSegment;g=e.fallback;e=e.children;var l=new Set,m={id:null,
rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,forceClientRender:!1,completedSegments:[],byteSize:0,fallbackAbortableTasks:l,errorDigest:null,resources:new Set},q=tg(a,c.chunks.length,m,c.formatContext,!1,!1);c.children.push(q);c.lastPushedText=!1;var t=tg(a,0,null,c.formatContext,!1,!1);t.parentFlushed=!0;b.blockedBoundary=m;b.blockedSegment=t;a.resources.boundaryResources=m.resources;try{if(Dg(a,b,e,0),t.lastPushedText&&t.textEmbedded&&t.chunks.push(Kb),t.status=1,Eg(m,t),0===m.pendingTasks)break a}catch(n){t.status=
4,m.forceClientRender=!0,"object"===typeof n&&null!==n&&n.$$typeof===kf?(a.onPostpone(n.message),f="POSTPONE"):f=yg(a,n),m.errorDigest=f}finally{a.resources.boundaryResources=d?d.resources:null,b.blockedBoundary=d,b.blockedSegment=c}b=ug(a,null,g,d,q,l,b.keyPath,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof d&&null!==d)switch(d.$$typeof){case Ye:d=d.render;Mf={};Nf=b;Sf=Rf=0;Tf=c;f=d(e,g);e=Zf(d,e,f,g);if(0!==Rf){d=b.treeContext;b.treeContext=Af(d,1,0);
try{Y(a,b,null,e,0)}finally{b.treeContext=d}}else Y(a,b,null,e,0);return;case af:d=d.type;e=Bg(d,e);Cg(a,b,c,d,e,g);return;case Ve:f=e.children;d=d._context;e=e.value;c=d._currentValue;d._currentValue=e;g=qf;qf=e={parent:g,depth:null===g?0:g.depth+1,context:d,parentValue:c,value:e};b.context=e;Y(a,b,null,f,0);a=qf;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");e=a.parentValue;a.context._currentValue=e===hf?a.context._defaultValue:e;a=qf=a.parent;
b.context=a;return;case We:e=e.children;e=e(d._currentValue);Y(a,b,null,e,0);return;case bf:f=d._init;d=f(d._payload);e=Bg(d,e);Cg(a,b,c,d,e,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==d?d:typeof d)+"."));}}
function Y(a,b,c,d,e){b.node=d;if("object"===typeof d&&null!==d){switch(d.$$typeof){case Pe:var g=d.type,f=d.key,l=d.props;d=d.ref;var m=nf(g),q=b.keyPath;b.keyPath=[b.keyPath,m,null==f?e:f];Cg(a,b,c,g,l,d);b.keyPath=q;return;case Qe:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");case bf:c=d._init;d=c(d._payload);Y(a,b,null,d,e);return}if(Pa(d)){Fg(a,b,d,e);return}if(c=mf(d))if(c=c.call(d)){d=c.next();
if(!d.done){g=[];do g.push(d.value),d=c.next();while(!d.done);Fg(a,b,g,e)}return}if("function"===typeof d.then)return Y(a,b,null,ig(d),e);if(d.$$typeof===We||d.$$typeof===Xe)return Y(a,b,null,d._currentValue,e);a=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===a?"object with keys {"+Object.keys(d).join(", ")+"}":a)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,
e.lastPushedText=Lb(b.blockedSegment.chunks,d,a.responseState,e.lastPushedText)):"number"===typeof d&&(e=b.blockedSegment,e.lastPushedText=Lb(b.blockedSegment.chunks,""+d,a.responseState,e.lastPushedText))}function Fg(a,b,c,d){for(var e=b.keyPath,g=c.length,f=0;f<g;f++){var l=b.treeContext;b.treeContext=Af(l,g,f);try{var m=c[f];if(Pa(m)||mf(m))b.keyPath=[b.keyPath,"",d];Dg(a,b,m,f)}finally{b.treeContext=l,b.keyPath=e}}}
function Dg(a,b,c,d){var e=b.blockedSegment,g=e.children.length,f=e.chunks.length,l=b.blockedSegment.formatContext,m=b.legacyContext,q=b.context,t=b.keyPath;try{return Y(a,b,null,c,d)}catch(n){if($f(),e.children.length=g,e.chunks.length=f,c=n===Ff?Jf():n,"object"===typeof c&&null!==c&&"function"===typeof c.then)d=ag(),e=b.blockedSegment,g=tg(a,e.chunks.length,null,e.formatContext,e.lastPushedText,!0),e.children.push(g),e.lastPushedText=!1,a=ug(a,d,b.node,b.blockedBoundary,g,b.abortSet,b.keyPath,b.legacyContext,
b.context,b.treeContext).ping,c.then(a,a),b.blockedSegment.formatContext=l,b.legacyContext=m,b.context=q,b.keyPath=t,wf(q);else throw b.blockedSegment.formatContext=l,b.legacyContext=m,b.context=q,b.keyPath=t,wf(q),c;}}function Gg(a){var b=a.blockedBoundary;a=a.blockedSegment;a.status=3;Hg(this,b,a)}
function Ig(a,b,c){var d=a.blockedBoundary;a.blockedSegment.status=3;null===d?(b.allPendingTasks--,1!==b.status&&2!==b.status&&(yg(b,c),zg(b,c))):(d.pendingTasks--,d.forceClientRender||(d.forceClientRender=!0,d.errorDigest=b.onError(c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(e){return Ig(e,b,c)}),d.fallbackAbortableTasks.clear(),b.allPendingTasks--,0===b.allPendingTasks&&(a=b.onAllReady,a()))}
function Eg(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&Eg(a,c)}else a.completedSegments.push(b)}
function Hg(a,b,c){if(null===b){if(c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&(a.onShellError=rg,b=a.onShellReady,b())}else b.pendingTasks--,b.forceClientRender||(0===b.pendingTasks?(c.parentFlushed&&1===c.status&&Eg(b,c),b.parentFlushed&&a.completedBoundaries.push(b),b.fallbackAbortableTasks.forEach(Gg,a),b.fallbackAbortableTasks.clear()):c.parentFlushed&&
1===c.status&&(Eg(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&(a=a.onAllReady,a())}
function xg(a){if(2!==a.status){var b=qf,c=og.current;og.current=mg;var d=pg.current;pg.current=ng;var e=vg;vg=a;var g=lg;lg=a.responseState;try{var f=a.pingedTasks,l;for(l=0;l<f.length;l++){var m=f[l];var q=a,t=m.blockedBoundary;q.resources.boundaryResources=t?t.resources:null;var n=m.blockedSegment;if(0===n.status){wf(m.context);var u=n.children.length,H=n.chunks.length;try{var I=m.thenableState;m.thenableState=null;Y(q,m,I,m.node,0);n.lastPushedText&&n.textEmbedded&&n.chunks.push(Kb);m.abortSet.delete(m);
n.status=1;Hg(q,m.blockedBoundary,n)}catch(C){$f();n.children.length=u;n.chunks.length=H;var E=C===Ff?Jf():C;if("object"===typeof E&&null!==E&&"function"===typeof E.then){var r=m.ping;E.then(r,r);m.thenableState=ag()}else{m.abortSet.delete(m);n.status=4;var O=void 0,P=q,L=m.blockedBoundary,B=E;"object"===typeof B&&null!==B&&B.$$typeof===kf?(P.onPostpone(B.message),O="POSTPONE"):O=yg(P,B);null===L?zg(P,B):(L.pendingTasks--,L.forceClientRender||(L.forceClientRender=!0,L.errorDigest=O,L.parentFlushed&&
P.clientRenderedBoundaries.push(L)));P.allPendingTasks--;if(0===P.allPendingTasks){var Q=P.onAllReady;Q()}}}finally{q.resources.boundaryResources=null}}}f.splice(0,l);null!==a.destination&&Jg(a,a.destination)}catch(C){yg(a,C),zg(a,C)}finally{lg=g,og.current=c,pg.current=d,c===mg&&wf(b),vg=e}}}
function Kg(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:var d=c.id=a.nextSegmentId++;c.lastPushedText=!1;c.textEmbedded=!1;a=a.responseState;p(b,vc);p(b,a.placeholderPrefix);a=d.toString(16);p(b,a);return v(b,wc);case 1:c.status=2;var e=!0;d=c.chunks;var g=0;c=c.children;for(var f=0;f<c.length;f++){for(e=c[f];g<e.index;g++)p(b,d[g]);e=Lg(a,b,e)}for(;g<d.length-1;g++)p(b,d[g]);g<d.length&&(e=v(b,d[g]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function Lg(a,b,c){var d=c.boundary;if(null===d)return Kg(a,b,c);d.parentFlushed=!0;if(d.forceClientRender)d=d.errorDigest,v(b,Ac),p(b,Cc),d&&(p(b,Ec),p(b,y(d)),p(b,Dc)),v(b,Fc),Kg(a,b,c);else if(0<d.pendingTasks){d.rootSegmentID=a.nextSegmentId++;0<d.completedSegments.length&&a.partialBoundaries.push(d);var e=a.responseState;var g=e.nextSuspenseID++;e=w(e.boundaryPrefix+g.toString(16));d=d.id=e;Gc(b,a.responseState,d);Kg(a,b,c)}else if(d.byteSize>a.progressiveChunkSize)d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),
Gc(b,a.responseState,d.id),Kg(a,b,c);else{(c=a.resources.boundaryResources)&&d.resources.forEach(Ne,c);v(b,xc);c=d.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");Lg(a,b,c[0])}return v(b,Bc)}function Mg(a,b,c){Dd(b,a.responseState,c.formatContext,c.id);Lg(a,b,c);return Ed(b,c.formatContext)}
function Ng(a,b,c){a.resources.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)Og(a,b,c,d[e]);d.length=0;se(b,c.resources,a.responseState);a=a.responseState;d=c.id;e=c.rootSegmentID;c=c.resources;var g=a.stylesToHoist;a.stylesToHoist=!1;var f=0===a.streamingFormat;f?(p(b,a.startInlineScript),g?0===(a.instructions&2)?(a.instructions|=10,p(b,2048<Nd.length?Nd.slice():Nd)):0===(a.instructions&8)?(a.instructions|=8,p(b,Od)):p(b,Pd):0===(a.instructions&2)?(a.instructions|=
2,p(b,Ld)):p(b,Md)):g?p(b,Vd):p(b,Ud);if(null===d)throw Error("An ID must have been assigned before we can complete the boundary.");e=e.toString(16);p(b,d);f?p(b,Qd):p(b,Wd);p(b,a.segmentPrefix);p(b,e);g?f?(p(b,Rd),Je(b,c)):(p(b,Xd),Ke(b,c)):f&&p(b,Sd);d=f?v(b,Td):v(b,pb);return uc(b,a)&&d}
function Og(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return Mg(a,b,d)}Mg(a,b,d);a=a.responseState;(c=0===a.streamingFormat)?(p(b,a.startInlineScript),0===(a.instructions&1)?(a.instructions|=1,p(b,Fd)):p(b,Gd)):p(b,Jd);p(b,a.segmentPrefix);e=e.toString(16);p(b,e);c?p(b,Hd):p(b,Kd);p(b,a.placeholderPrefix);p(b,e);b=c?v(b,Id):v(b,pb);return b}
function Jg(a,b){h=new Uint8Array(2048);k=0;sa=!0;try{var c,d=a.completedRootSegment;if(null!==d)if(0===a.pendingRootTasks){var e=a.resources,g=a.responseState;if(0!==a.allPendingTasks&&g.externalRuntimeScript){var f=g.externalRuntimeScript,l=f.chunks,m="[script]"+f.src,q=e.scriptsMap.get(m);q||(q={type:"script",chunks:l,state:0,props:null},e.scriptsMap.set(m,q),e.scripts.add(q))}var t=g.htmlChunks,n=g.headChunks;f=0;if(t){for(f=0;f<t.length;f++)p(b,t[f]);if(n)for(f=0;f<n.length;f++)p(b,n[f]);else p(b,
U("head")),p(b,N)}else if(n)for(f=0;f<n.length;f++)p(b,n[f]);var u=g.charsetChunks;for(f=0;f<u.length;f++)p(b,u[f]);u.length=0;e.preconnects.forEach(te,b);e.preconnects.clear();var H=g.preconnectChunks;for(f=0;f<H.length;f++)p(b,H[f]);H.length=0;e.fontPreloads.forEach(te,b);e.fontPreloads.clear();e.highImagePreloads.forEach(te,b);e.highImagePreloads.clear();e.precedences.forEach(Ce,b);e.bootstrapScripts.forEach(te,b);e.scripts.forEach(te,b);e.scripts.clear();e.bulkPreloads.forEach(te,b);e.bulkPreloads.clear();
var I=g.preloadChunks;for(f=0;f<I.length;f++)p(b,I[f]);I.length=0;var E=g.hoistableChunks;for(f=0;f<E.length;f++)p(b,E[f]);E.length=0;t&&null===n&&(p(b,mc),p(b,"head"),p(b,nc));Lg(a,b,d);a.completedRootSegment=null;uc(b,a.responseState)}else return;else if(0<a.pendingRootTasks)return;var r=a.resources,O=a.responseState;d=0;r.preconnects.forEach(ue,b);r.preconnects.clear();var P=O.preconnectChunks;for(d=0;d<P.length;d++)p(b,P[d]);P.length=0;r.fontPreloads.forEach(ue,b);r.fontPreloads.clear();r.highImagePreloads.forEach(te,
b);r.highImagePreloads.clear();r.precedences.forEach(Ee,b);r.scripts.forEach(ue,b);r.scripts.clear();r.bulkPreloads.forEach(ue,b);r.bulkPreloads.clear();var L=O.preloadChunks;for(d=0;d<L.length;d++)p(b,L[d]);L.length=0;var B=O.hoistableChunks;for(d=0;d<B.length;d++)p(b,B[d]);B.length=0;var Q=a.clientRenderedBoundaries;for(c=0;c<Q.length;c++){var C=Q[c];r=b;var D=a.responseState,ha=C.id,Fa=C.errorDigest,ua=C.errorMessage,ia=C.errorComponentStack,ca=0===D.streamingFormat;ca?(p(r,D.startInlineScript),
0===(D.instructions&4)?(D.instructions|=4,p(r,Yd)):p(r,Zd)):p(r,ce);if(null===ha)throw Error("An ID must have been assigned before we can complete the boundary.");p(r,ha);ca&&p(r,$d);if(Fa||ua||ia)ca?(p(r,ae),p(r,he(Fa||""))):(p(r,de),p(r,y(Fa||"")));if(ua||ia)ca?(p(r,ae),p(r,he(ua||""))):(p(r,ee),p(r,y(ua||"")));ia&&(ca?(p(r,ae),p(r,he(ia))):(p(r,fe),p(r,y(ia))));if(ca?!v(r,be):!v(r,pb)){a.destination=null;c++;Q.splice(0,c);return}}Q.splice(0,c);var va=a.completedBoundaries;for(c=0;c<va.length;c++)if(!Ng(a,
b,va[c])){a.destination=null;c++;va.splice(0,c);return}va.splice(0,c);za(b);h=new Uint8Array(2048);k=0;sa=!0;var ja=a.partialBoundaries;for(c=0;c<ja.length;c++){var ka=ja[c];a:{Q=a;C=b;Q.resources.boundaryResources=ka.resources;var la=ka.completedSegments;for(D=0;D<la.length;D++)if(!Og(Q,C,ka,la[D])){D++;la.splice(0,D);var Ga=!1;break a}la.splice(0,D);Ga=se(C,ka.resources,Q.responseState)}if(!Ga){a.destination=null;c++;ja.splice(0,c);return}}ja.splice(0,c);var Z=a.completedBoundaries;for(c=0;c<Z.length;c++)if(!Ng(a,
b,Z[c])){a.destination=null;c++;Z.splice(0,c);return}Z.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?(a.flushScheduled=!1,a=a.responseState,a.hasBody&&(p(b,mc),p(b,"body"),p(b,nc)),a.htmlChunks&&(p(b,mc),p(b,"html"),p(b,nc)),za(b),ra(b),b.end()):(za(b),ra(b))}}function Pg(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return Oe.run(a,xg,a)})}
function Me(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setImmediate(function(){return Jg(a,b)})}}function Qg(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return Ig(e,a,d)});c.clear()}null!==a.destination&&Jg(a,a.destination)}catch(e){yg(a,e),zg(a,e)}}
function Rg(a){return{write:function(b){return a.push(b)},end:function(){a.push(null)},destroy:function(b){a.destroy(b)}}}
exports.prerenderToNodeStreams=function(a,b){return new Promise(function(c,d){var e={preloadsMap:new Map,preconnectsMap:new Map,stylesMap:new Map,scriptsMap:new Map,preconnects:new Set,fontPreloads:new Set,highImagePreloads:new Set,precedences:new Map,stylePrecedences:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,boundaryResources:null},g=sg(a,e,Gb(e,b?b.identifierPrefix:void 0,void 0,b?b.bootstrapScriptContent:void 0,b?b.bootstrapScripts:void 0,b?b.bootstrapModules:void 0,
b?b.unstable_externalRuntimeSrc:void 0),Ib(b?b.namespaceURI:void 0),b?b.progressiveChunkSize:void 0,b?b.onError:void 0,function(){var m=new aa.Readable({read:function(){if(1===g.status)g.status=2,q.destroy(g.fatalError);else if(2!==g.status&&null===g.destination){g.destination=q;try{Jg(g,q)}catch(t){yg(g,t),zg(g,t)}}}}),q=Rg(m);c({prelude:m})},void 0,void 0,d,b?b.onPostpone:void 0);if(b&&b.signal){var f=b.signal;if(f.aborted)Qg(g,f.reason);else{var l=function(){Qg(g,f.reason);f.removeEventListener("abort",
l)};f.addEventListener("abort",l)}}Pg(g)})};exports.version="18.3.0-experimental-dd480ef92-20230822";
