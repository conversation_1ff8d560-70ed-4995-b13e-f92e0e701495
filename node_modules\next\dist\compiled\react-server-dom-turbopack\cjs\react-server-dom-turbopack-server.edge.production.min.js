/**
 * @license React
 * react-server-dom-turbopack-server.edge.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("react"),ba=require("react-dom"),m=null,n=0;function p(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<n&&(a.enqueue(new Uint8Array(m.buffer,0,n)),m=new Uint8Array(512),n=0),a.enqueue(b);else{var d=m.length-n;d<b.byteLength&&(0===d?a.enqueue(m):(m.set(b.subarray(0,d),n),a.enqueue(m),b=b.subarray(d)),m=new Uint8Array(512),n=0);m.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function u(a,b,d){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===t){var b=ea.call(arguments,1);a.$$typeof=t;a.$$id=this.$$id;a.$$bound=this.$$bound?this.$$bound.concat(b):b}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ja={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];
case "__esModule":var d=a.$$id;a.default=u(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=u({},a.$$id,!0),e=new Proxy(c,ja);a.status="fulfilled";a.value=e;return a.then=u(function(f){return Promise.resolve(f(e))},
a.$$id+"#then",!1)}c=a[b];c||(c=u(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");
}},ra={prefetchDNS:ka,preconnect:la,preload:ma,preloadModule:na,preinitStyle:oa,preinitScript:pa,preinitModuleScript:qa};function ka(a){if("string"===typeof a&&a){var b=v();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),w(b,"D",a))}}}function la(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?w(d,"C",[a,b]):w(d,"C",a))}}}
function ma(a,b,d){if("string"===typeof a){var c=v();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,k=d.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=y(d))?w(c,"L",[a,b,d]):w(c,"L",[a,b]))}}}function na(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=y(b))?w(d,"m",[a,b]):w(d,"m",a)}}}
function oa(a,b,d){if("string"===typeof a){var c=v();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=y(d))?w(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?w(c,"S",[a,b]):w(c,"S",a)}}}function pa(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=y(b))?w(d,"X",[a,b]):w(d,"X",a)}}}function qa(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=y(b))?w(d,"M",[a,b]):w(d,"M",a)}}}
function y(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var sa=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,ta="function"===typeof AsyncLocalStorage,ua=ta?new AsyncLocalStorage:null,z=Symbol.for("react.element"),va=Symbol.for("react.fragment"),wa=Symbol.for("react.server_context"),xa=Symbol.for("react.forward_ref"),ya=Symbol.for("react.suspense"),za=Symbol.for("react.suspense_list"),Aa=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Ba=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.postpone");
var Ca=Symbol.iterator,B=null;function C(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");C(a,d);b.context._currentValue=b.value}}}function Da(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Da(a)}
function Ea(a){var b=a.parent;null!==b&&Ea(b);a.context._currentValue=a.value}function Fa(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?C(a,b):Fa(a,b)}
function Ga(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?C(a,d):Ga(a,d);b.context._currentValue=b.value}var Ha=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ia(){}function Ja(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Ia,Ia),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}D=b;throw Ha;}}var D=null;
function Ka(){if(null===D)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=D;D=null;return a}var E=null,F=0,G=null;function La(){var a=G;G=null;return a}function Ma(a){return a._currentValue}
var Qa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:H,useTransition:H,readContext:Ma,useContext:Ma,useReducer:H,useRef:H,useState:H,useInsertionEffect:H,useLayoutEffect:H,useImperativeHandle:H,useEffect:H,useId:Na,useSyncExternalStore:H,useCacheRefresh:function(){return Oa},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ba;return b},use:Pa};
function H(){throw Error("This Hook is not supported in Server Components.");}function Oa(){throw Error("Refreshing the cache is not supported in Server Components.");}function Na(){if(null===E)throw Error("useId can only be used while React is rendering");var a=E.identifierCount++;return":"+E.identifierPrefix+"S"+a.toString(32)+":"}
function Pa(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=F;F+=1;null===G&&(G=[]);return Ja(G,a,b)}if(a.$$typeof===wa)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Ra(){return(new AbortController).signal}function Sa(){var a=v();return a?a.cache:new Map}
var Ta={getCacheSignal:function(){var a=Sa(),b=a.get(Ra);void 0===b&&(b=Ra(),a.set(Ra,b));return b},getCacheForType:function(a){var b=Sa(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},Ua=Array.isArray,Va=Object.getPrototypeOf;function Wa(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function Xa(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Ua(a))return"[...]";a=Wa(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function I(a){if("string"===typeof a)return a;switch(a){case ya:return"Suspense";case za:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case xa:return I(a.render);case Aa:return I(a.type);case A:var b=a._payload;a=a._init;try{return I(a(b))}catch(d){}}return""}
function J(a,b){var d=Wa(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(Ua(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?J(g):Xa(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===z)e="<"+I(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var k=f[g],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h="object"===typeof h&&null!==h?J(h):
Xa(h);k===b?(d=e.length,c=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var Ya=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Za=aa.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;if(!Za)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');
var $a=Object.prototype,K=JSON.stringify,ab=Za.ReactCurrentCache,bb=Ya.ReactCurrentDispatcher;function cb(a){console.error(a)}function db(){}
function eb(a,b,d,c,e,f){if(null!==ab.current&&ab.current!==Ta)throw Error("Currently React only supports one RSC renderer at a time.");sa.current=ra;ab.current=Ta;var g=new Set;c=[];var k=new Set,h={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:g,pingedTasks:c,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:[],onError:void 0===d?cb:d,onPostpone:void 0===f?db:f,toJSON:function(l,x){return fb(h,this,l,x)}};h.pendingChunks++;a=L(h,a,null,g);c.push(a);return h}var M=null;function v(){if(M)return M;if(ta){var a=ua.getStore();if(a)return a}return null}
function gb(a,b){a.pendingChunks++;var d=L(a,null,B,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,hb(a,d),d.id;case "rejected":var c=N(a,b.reason);O(a,d.id,c);return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=e;hb(a,d)},function(e){d.status=4;a.abortableTasks.delete(d);e=N(a,e);O(a,
d.id,e);null!==a.destination&&P(a,a.destination)});return d.id}function w(a,b,d){d=K(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=q.encode(b+d+"\n");a.completedHintChunks.push(d);ib(a)}function jb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function kb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:jb}}
function Q(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===r)return[z,b,d,e];F=0;G=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:kb(e):e}if("string"===typeof b)return[z,b,d,e];if("symbol"===typeof b)return b===va?e.children:[z,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===r)return[z,b,d,e];switch(b.$$typeof){case A:var g=
b._init;b=g(b._payload);return Q(a,b,d,c,e,f);case xa:return a=b.render,F=0,G=f,a(e,void 0);case Aa:return Q(a,b.type,d,c,e,f)}}throw Error("Unsupported Server Component type: "+Xa(b));}function hb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return lb(a)},0))}function L(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return hb(a,e)},thenableState:null};c.add(e);return e}
function R(a){return"$"+a.toString(16)}function mb(a,b,d){a=K(d);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function nb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===z&&"1"===d?"$L"+g.toString(16):R(g);try{var k=a.bundlerConfig,h=c.$$id;g="";var l=k[h];if(l)g=l.name;else{var x=h.lastIndexOf("#");-1!==x&&(g=h.slice(x+1),l=k[h.slice(0,x)]);if(!l)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var Eb=!0===c.$$async?[l.id,l.chunks,g,1]:[l.id,l.chunks,
g];a.pendingChunks++;var Y=a.nextChunkId++,Fb=K(Eb),Gb=Y.toString(16)+":I"+Fb+"\n",Hb=q.encode(Gb);a.completedImportChunks.push(Hb);f.set(e,Y);return b[0]===z&&"1"===d?"$L"+Y.toString(16):R(Y)}catch(Ib){return a.pendingChunks++,b=a.nextChunkId++,d=N(a,Ib),O(a,b,d),R(b)}}function S(a,b){a.pendingChunks++;b=L(a,b,B,a.abortableTasks);ob(a,b);return b.id}var T=!1;
function fb(a,b,d,c){switch(c){case z:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===z||c.$$typeof===A);)try{switch(c.$$typeof){case z:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=S(a,c);return R(g)}if(T===c)T=null;else return R(f)}else e.set(c,-1);var k=c;c=Q(a,k.type,k.key,k.ref,k.props,null);break;case A:var h=c._init;c=h(c._payload)}}catch(l){b=l===Ha?Ka():l;if("object"===typeof b&&null!==b&&"function"===typeof b.then)return a.pendingChunks++,a=L(a,c,B,a.abortableTasks),
c=a.ping,b.then(c,c),a.thenableState=La(),"$L"+a.id.toString(16);a.pendingChunks++;c=a.nextChunkId++;b=N(a,b);O(a,c,b);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){if(c.$$typeof===r)return nb(a,b,d,c);b=a.writtenObjects;d=b.get(c);if("function"===typeof c.then){if(void 0!==d)if(T===c)T=null;else return"$@"+d.toString(16);a=gb(a,c);b.set(c,a);return"$@"+a.toString(16)}if(void 0!==d){if(-1===d)return a=S(a,c),R(a);if(T===c)T=null;else return R(d)}else b.set(c,-1);if(Ua(c))return c;
if(c instanceof Map){c=Array.from(c);for(b=0;b<c.length;b++)d=c[b][0],"object"===typeof d&&null!==d&&(e=a.writtenObjects,void 0===e.get(d)&&e.set(d,-1));return"$Q"+S(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(b=0;b<c.length;b++)d=c[b],"object"===typeof d&&null!==d&&(e=a.writtenObjects,void 0===e.get(d)&&e.set(d,-1));return"$W"+S(a,c).toString(16)}null===c||"object"!==typeof c?a=null:(a=Ca&&c[Ca]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=Va(c);if(a!==
$a&&(null===a||null!==Va(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,b=a.nextChunkId++,c=q.encode(c),d=c.byteLength,d=b.toString(16)+":T"+d.toString(16)+",",d=q.encode(d),a.completedRegularChunks.push(d,c),R(b);a="$"===c[0]?"$"+c:c;return a}if("boolean"===
typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){if(c.$$typeof===r)return nb(a,b,d,c);if(c.$$typeof===t)return b=a.writtenServerReferences,d=b.get(c),void 0!==d?a="$F"+d.toString(16):(d=c.$$bound,d={id:c.$$id,bound:d?Promise.resolve(d):null},a=S(a,d),b.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+
J(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+J(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==f)return R(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+
J(b,d));a.pendingChunks++;b=a.nextChunkId++;d=mb(a,b,"$S"+f);a.completedImportChunks.push(d);e.set(c,b);return R(b)}if("bigint"===typeof c)return"$n"+c.toString(10);throw Error("Type "+typeof c+" is not supported in Client Component props."+J(b,d));}
function N(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}function pb(a,b){null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}
function O(a,b,d){d={digest:d};b=b.toString(16)+":E"+K(d)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}
function ob(a,b){if(0===b.status){var d=B,c=b.context;d!==c&&(null===d?Ea(c):null===c?Da(d):d.depth===c.depth?C(d,c):d.depth>c.depth?Fa(d,c):Ga(d,c),B=c);try{var e=b.model;if("object"===typeof e&&null!==e&&e.$$typeof===z){a.writtenObjects.set(e,b.id);d=e;var f=b.thenableState;b.model=e;e=Q(a,d.type,d.key,d.ref,d.props,f);for(b.thenableState=null;"object"===typeof e&&null!==e&&e.$$typeof===z;)a.writtenObjects.set(e,b.id),f=e,b.model=e,e=Q(a,f.type,f.key,f.ref,f.props,null)}"object"===typeof e&&null!==
e&&a.writtenObjects.set(e,b.id);var g=b.id;T=e;var k=K(e,a.toJSON),h=g.toString(16)+":"+k+"\n",l=q.encode(h);a.completedRegularChunks.push(l);a.abortableTasks.delete(b);b.status=1}catch(x){g=x===Ha?Ka():x,"object"===typeof g&&null!==g&&"function"===typeof g.then?(a=b.ping,g.then(a,a),b.thenableState=La()):(a.abortableTasks.delete(b),b.status=4,g=N(a,g),O(a,b.id,g))}}}
function lb(a){var b=bb.current;bb.current=Qa;var d=M;E=M=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)ob(a,c[e]);null!==a.destination&&P(a,a.destination)}catch(f){N(a,f),pb(a,f)}finally{bb.current=b,E=null,M=d}}
function P(a,b){m=new Uint8Array(512);n=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,p(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)p(b,e[c]);e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)a.pendingChunks--,p(b,f[c]);f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)a.pendingChunks--,p(b,g[c]);g.splice(0,c)}finally{a.flushScheduled=!1,m&&0<n&&(b.enqueue(new Uint8Array(m.buffer,0,n)),m=null,n=0)}0===a.pendingChunks&&
b.close()}function qb(a){a.flushScheduled=null!==a.destination;ta?setTimeout(function(){return ua.run(a,lb,a)},0):setTimeout(function(){return lb(a)},0)}function ib(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setTimeout(function(){return P(a,b)},0)}}
function rb(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++,e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=N(a,e);O(a,c,f,e);d.forEach(function(g){g.status=3;var k=R(c);g=mb(a,g.id,k);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&P(a,a.destination)}catch(g){N(a,g),pb(a,g)}}
function sb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var U=new Map;
function tb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function ub(){}
function vb(a){for(var b=a[1],d=[],c=0;c<b.length;c++){var e=b[c],f=U.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);d.push(f);var g=U.set.bind(U,e,null);f.then(g,ub);U.set(e,f)}else null!==f&&d.push(f)}return 4===a.length?0===d.length?tb(a[0]):Promise.all(d).then(function(){return tb(a[0])}):0<d.length?Promise.all(d):null}
function V(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function wb(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}wb.prototype=Object.create(Promise.prototype);
wb.prototype.then=function(a,b){switch(this.status){case "resolved_model":xb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function yb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function zb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&yb(d,b)}}function Ab(a,b,d,c,e,f){var g=sb(a._bundlerConfig,b);a=vb(g);if(d)d=Promise.all([d,a]).then(function(k){k=k[0];var h=V(g);return h.bind.apply(h,[null].concat(k))});else if(a)d=Promise.resolve(a).then(function(){return V(g)});else return V(g);d.then(Bb(c,e,f),Cb(c));return null}var W=null,X=null;
function xb(a){var b=W,d=X;W=a;X=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==X&&0<X.deps?(X.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{W=b,X=d}}function Db(a,b){a._chunks.forEach(function(d){"pending"===d.status&&zb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new wb("resolved_model",c,null,a):new wb("pending",null,null,a),d.set(b,c));return c}function Bb(a,b,d){if(X){var c=X;c.deps++}else c=X={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&yb(e,c.value))}}function Cb(a){return function(b){return zb(a,b)}}
function Jb(a,b){a=Z(a,b);"resolved_model"===a.status&&xb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Kb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Jb(a,c),Ab(a,c.id,c.bound,W,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Jb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Jb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(e)&&f.append(k.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":xb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=W,a.then(Bb(c,b,d),Cb(c)),null;default:throw a.reason;}}return c}
function Lb(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Kb(e,this,f,g):g}};return e}function Mb(a){Db(a,Error("Connection closed."))}function Nb(a,b,d){var c=sb(a,b);a=vb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=V(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return V(c)}):Promise.resolve(V(c))}
function Ob(a,b,d){a=Lb(b,d,a);Mb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=u({},a,!1);return new Proxy(a,ja)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=Ob(a,b,e),c=Nb(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=Nb(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=Lb(b,"",a);Mb(a);return Z(a,0)};
exports.registerClientReference=function(a,b,d){return u(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=eb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)rb(c,e.reason);else{var f=function(){rb(c,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){qb(c)},pull:function(g){if(1===c.status)c.status=2,ca(g,c.fatalError);else if(2!==c.status&&null===c.destination){c.destination=g;try{P(c,
g)}catch(k){N(c,k),pb(c,k)}}},cancel:function(){}},{highWaterMark:0})};
