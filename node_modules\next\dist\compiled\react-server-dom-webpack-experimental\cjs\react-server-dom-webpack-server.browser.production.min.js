/**
 * @license React
 * react-server-dom-webpack-server.browser.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var aa=require("react-dom"),ba=require("react"),l=null,n=0;function p(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<n&&(a.enqueue(new Uint8Array(l.buffer,0,n)),l=new Uint8Array(512),n=0),a.enqueue(b);else{var d=l.length-n;d<b.byteLength&&(0===d?a.enqueue(l):(l.set(b.subarray(0,d),n),a.enqueue(l),b=b.subarray(d)),l=new Uint8Array(512),n=0);l.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function u(a,b,d){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===t){var b=ea.call(arguments,1);a.$$typeof=t;a.$$id=this.$$id;a.$$bound=this.$$bound?this.$$bound.concat(b):b}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ja(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "__esModule":var d=a.$$id;a.default=u(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=u({},a.$$id,!0),e=new Proxy(c,ka);a.status="fulfilled";a.value=e;return a.then=u(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}c=a[b];c||(c=u(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c}
var ka={get:function(a,b){return ja(a,b)},getOwnPropertyDescriptor:function(a,b){var d=Object.getOwnPropertyDescriptor(a,b);d||(d={value:ja(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,d));return d},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ta={prefetchDNS:la,preconnect:ma,preload:na,preloadModule:oa,preinitStyle:pa,preinitScript:qa,preinitModuleScript:ra};
function la(a){if("string"===typeof a&&a){var b=v?v:null;if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),w(b,"D",a))}}}function ma(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?w(d,"C",[a,b]):w(d,"C",a))}}}
function na(a,b,d){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,h=d.imageSizes,k="";"string"===typeof g&&""!==g?(k+="["+g+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;f+="[image]"+k}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=x(d))?w(c,"L",[a,b,d]):w(c,"L",[a,b]))}}}function oa(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"m",[a,b]):w(d,"m",a)}}}
function pa(a,b,d){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=x(d))?w(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?w(c,"S",[a,b]):w(c,"S",a)}}}function qa(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"X",[a,b]):w(d,"X",a)}}}
function ra(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"M",[a,b]):w(d,"M",a)}}}function x(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var ua=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,y=Symbol.for("react.element"),va=Symbol.for("react.fragment"),wa=Symbol.for("react.provider"),xa=Symbol.for("react.server_context"),ya=Symbol.for("react.forward_ref"),za=Symbol.for("react.suspense"),Aa=Symbol.for("react.suspense_list"),Ba=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),B=Symbol.for("react.default_value"),Ca=Symbol.for("react.memo_cache_sentinel"),C=Symbol.for("react.postpone"),Da=Symbol.iterator,D=null;
function E(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");E(a,d);b.context._currentValue=b.value}}}function Ea(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ea(a)}
function Fa(a){var b=a.parent;null!==b&&Fa(b);a.context._currentValue=a.value}function Ga(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?E(a,b):Ga(a,b)}
function Ha(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?E(a,d):Ha(a,d);b.context._currentValue=b.value}function Ia(a){var b=D;b!==a&&(null===b?Fa(a):null===a?Ea(b):b.depth===a.depth?E(b,a):b.depth>a.depth?Ga(b,a):Ha(b,a),D=a)}function Ja(a,b){var d=a._currentValue;a._currentValue=b;var c=D;return D=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Ka=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function La(){}function Ma(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(La,La),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Na=b;throw Ka;}}var Na=null;
function Oa(){if(null===Na)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Na;Na=null;return a}var F=null,Pa=0,H=null;function Qa(){var a=H;H=null;return a}function Ra(a){return a._currentValue}
var Va={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:I,useTransition:I,readContext:Ra,useContext:Ra,useReducer:I,useRef:I,useState:I,useInsertionEffect:I,useLayoutEffect:I,useImperativeHandle:I,useEffect:I,useId:Sa,useSyncExternalStore:I,useCacheRefresh:function(){return Ta},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ca;return b},use:Ua};
function I(){throw Error("This Hook is not supported in Server Components.");}function Ta(){throw Error("Refreshing the cache is not supported in Server Components.");}function Sa(){if(null===F)throw Error("useId can only be used while React is rendering");var a=F.identifierCount++;return":"+F.identifierPrefix+"S"+a.toString(32)+":"}
function Ua(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Pa;Pa+=1;null===H&&(H=[]);return Ma(H,a,b)}if(a.$$typeof===xa)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Wa(){return(new AbortController).signal}function Xa(){var a=v?v:null;return a?a.cache:new Map}
var Ya={getCacheSignal:function(){var a=Xa(),b=a.get(Wa);void 0===b&&(b=Wa(),a.set(Wa,b));return b},getCacheForType:function(a){var b=Xa(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},Za=Array.isArray,$a=Object.getPrototypeOf;function ab(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function bb(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Za(a))return"[...]";a=ab(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function cb(a){if("string"===typeof a)return a;switch(a){case za:return"Suspense";case Aa:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case ya:return cb(a.render);case Ba:return cb(a.type);case z:var b=a._payload;a=a._init;try{return cb(a(b))}catch(d){}}return""}
function J(a,b){var d=ab(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(Za(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?J(g):bb(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===y)e="<"+cb(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var h=f[g],k=JSON.stringify(h);e+=('"'+h+'"'===k?h:k)+": ";k=a[h];k="object"===typeof k&&null!==k?J(k):
bb(k);h===b?(d=e.length,c=k.length,e+=k):e=10>k.length&&40>e.length+k.length?e+k:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var db=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,eb=db.ContextRegistry,K=ba.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!K)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var fb=Object.prototype,L=JSON.stringify,gb=K.TaintRegistryObjects,M=K.TaintRegistryValues,hb=K.TaintRegistryByteLengths,ib=K.TaintRegistryPendingRequests,jb=K.ReactCurrentCache,kb=db.ReactCurrentDispatcher;function N(a){throw Error(a);}
function lb(a){a=a.taintCleanupQueue;ib.delete(a);for(var b=0;b<a.length;b++){var d=a[b],c=M.get(d);void 0!==c&&(1===c.count?M.delete(d):c.count--)}a.length=0}function mb(a){console.error(a)}function nb(){}
function ob(a,b,d,c,e,f){if(null!==jb.current&&jb.current!==Ya)throw Error("Currently React only supports one RSC renderer at a time.");ua.current=ta;jb.current=Ya;var g=new Set,h=[],k=[];ib.add(k);var m=new Set,A={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:m,abortableTasks:g,pingedTasks:h,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:k,onError:void 0===d?mb:d,onPostpone:void 0===f?nb:f,toJSON:function(sa,G){return pb(A,this,sa,G)}};A.pendingChunks++;b=qb(c);a=rb(A,a,b,g);h.push(a);return A}var v=null,sb={};
function tb(a,b){a.pendingChunks++;var d=rb(a,null,D,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,ub(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===C?(vb(a,c.message),wb(a,d.id)):(c=O(a,c),P(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;ub(a,d)},function(e){d.status=4;a.abortableTasks.delete(d);e=O(a,e);P(a,d.id,e);null!==a.destination&&Q(a,a.destination)});return d.id}function w(a,b,d){d=L(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=q.encode(b+d+"\n");a.completedHintChunks.push(d);!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(d=a.destination,a.flushScheduled=!0,Q(a,d))}function xb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function yb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:z,_payload:a,_init:xb}}
function R(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===r)return[y,b,d,e];Pa=0;H=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:yb(e):e}if("string"===typeof b)return[y,b,d,e];if("symbol"===typeof b)return b===va?e.children:[y,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===r)return[y,b,d,e];switch(b.$$typeof){case z:var g=
b._init;b=g(b._payload);return R(a,b,d,c,e,f);case ya:return a=b.render,Pa=0,H=f,a(e,void 0);case Ba:return R(a,b.type,d,c,e,f);case wa:return Ja(b._context,e.value),[y,b,d,{value:e.value,children:e.children,__pop:sb}]}}throw Error("Unsupported Server Component type: "+bb(b));}function ub(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,zb(a))}
function rb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return ub(a,e)},thenableState:null};c.add(e);return e}function S(a){return"$"+a.toString(16)}function Ab(a,b,d){a=L(d);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function Bb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===y&&"1"===d?"$L"+g.toString(16):S(g);try{var h=a.bundlerConfig,k=c.$$id;g="";var m=h[k];if(m)g=m.name;else{var A=k.lastIndexOf("#");-1!==A&&(g=k.slice(A+1),m=h[k.slice(0,A)]);if(!m)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var sa=!0===c.$$async?[m.id,m.chunks,g,1]:[m.id,m.chunks,
g];a.pendingChunks++;var G=a.nextChunkId++,Vb=L(sa),Wb=G.toString(16)+":I"+Vb+"\n",Xb=q.encode(Wb);a.completedImportChunks.push(Xb);f.set(e,G);return b[0]===y&&"1"===d?"$L"+G.toString(16):S(G)}catch(Yb){return a.pendingChunks++,b=a.nextChunkId++,d=O(a,Yb),P(a,b,d),S(b)}}function T(a,b){a.pendingChunks++;b=rb(a,b,D,a.abortableTasks);Cb(a,b);return b.id}
function U(a,b,d){if(hb.has(d.byteLength)){var c=M.get(String.fromCharCode.apply(String,new Uint8Array(d.buffer,d.byteOffset,d.byteLength)));void 0!==c&&N(c.message)}a.pendingChunks+=2;c=a.nextChunkId++;var e=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);d=512<d.byteLength?e.slice():e;e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";b=q.encode(b);a.completedRegularChunks.push(b,d);return S(c)}var V=!1;
function pb(a,b,d,c){switch(c){case y:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===y||c.$$typeof===z);)try{switch(c.$$typeof){case y:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=T(a,c);return S(g)}if(V===c)V=null;else return S(f)}else e.set(c,-1);var h=c;c=R(a,h.type,h.key,h.ref,h.props,null);break;case z:var k=c._init;c=k(c._payload)}}catch(m){d=m===Ka?Oa():m;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=rb(a,c,D,a.abortableTasks),
c=a.ping,d.then(c,c),a.thenableState=Qa(),"$L"+a.id.toString(16);if(d.$$typeof===C)return c=d,a.pendingChunks++,d=a.nextChunkId++,vb(a,c.message),wb(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=O(a,d);P(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){e=gb.get(c);void 0!==e&&N(e);if(c.$$typeof===r)return Bb(a,b,d,c);b=a.writtenObjects;e=b.get(c);if("function"===typeof c.then){if(void 0!==e)if(V===c)V=null;else return"$@"+e.toString(16);a=tb(a,c);b.set(c,
a);return"$@"+a.toString(16)}if(c.$$typeof===wa)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=Ab(a,d,"$P"+c),a.completedRegularChunks.push(c)),S(d);if(c===sb){a=D;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===B?a.context._defaultValue:c;D=a.parent;return}if(void 0!==e){if(-1===e)return a=T(a,c),S(a);if(V===c)V=null;else return S(e)}else b.set(c,
-1);if(Za(c))return c;if(c instanceof Map){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d][0],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$Q"+T(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$W"+T(a,c).toString(16)}if(c instanceof ArrayBuffer)return U(a,"A",new Uint8Array(c));if(c instanceof Int8Array)return U(a,"C",c);if(c instanceof
Uint8Array)return U(a,"c",c);if(c instanceof Uint8ClampedArray)return U(a,"U",c);if(c instanceof Int16Array)return U(a,"S",c);if(c instanceof Uint16Array)return U(a,"s",c);if(c instanceof Int32Array)return U(a,"L",c);if(c instanceof Uint32Array)return U(a,"l",c);if(c instanceof Float32Array)return U(a,"F",c);if(c instanceof Float64Array)return U(a,"D",c);if(c instanceof BigInt64Array)return U(a,"N",c);if(c instanceof BigUint64Array)return U(a,"m",c);if(c instanceof DataView)return U(a,"V",c);null===
c||"object"!==typeof c?a=null:(a=Da&&c[Da]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=$a(c);if(a!==fb&&(null===a||null!==$a(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){e=M.get(c);void 0!==e&&N(e.message);if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,
c=q.encode(c),b=c.byteLength,b=d.toString(16)+":T"+b.toString(16)+",",b=q.encode(b),a.completedRegularChunks.push(b,c),S(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){e=gb.get(c);void 0!==e&&N(e);if(c.$$typeof===r)return Bb(a,b,d,c);if(c.$$typeof===t)return d=a.writtenServerReferences,
b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?Promise.resolve(b):null},a=T(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+J(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+J(b,d));}if("symbol"===typeof c){e=
a.writtenSymbols;f=e.get(c);if(void 0!==f)return S(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+J(b,d));a.pendingChunks++;d=a.nextChunkId++;b=Ab(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return S(d)}if("bigint"===typeof c)return a=M.get(c),void 0!==a&&N(a.message),"$n"+c.toString(10);throw Error("Type "+typeof c+
" is not supported in Client Component props."+J(b,d));}function vb(a,b){a=a.onPostpone;a(b)}function O(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}
function Db(a,b){lb(a);null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}function wb(a,b){b=b.toString(16)+":P\n";b=q.encode(b);a.completedErrorChunks.push(b)}function P(a,b,d){d={digest:d};b=b.toString(16)+":E"+L(d)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}
function Cb(a,b){if(0===b.status){Ia(b.context);try{var d=b.model;if("object"===typeof d&&null!==d&&d.$$typeof===y){a.writtenObjects.set(d,b.id);var c=d,e=b.thenableState;b.model=d;d=R(a,c.type,c.key,c.ref,c.props,e);for(b.thenableState=null;"object"===typeof d&&null!==d&&d.$$typeof===y;)a.writtenObjects.set(d,b.id),c=d,b.model=d,d=R(a,c.type,c.key,c.ref,c.props,null)}"object"===typeof d&&null!==d&&a.writtenObjects.set(d,b.id);var f=b.id;V=d;var g=L(d,a.toJSON),h=f.toString(16)+":"+g+"\n",k=q.encode(h);
a.completedRegularChunks.push(k);a.abortableTasks.delete(b);b.status=1}catch(m){f=m===Ka?Oa():m;if("object"===typeof f&&null!==f){if("function"===typeof f.then){a=b.ping;f.then(a,a);b.thenableState=Qa();return}if(f.$$typeof===C){a.abortableTasks.delete(b);b.status=4;vb(a,f.message);wb(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;f=O(a,f);P(a,b.id,f)}}}
function zb(a){var b=kb.current;kb.current=Va;var d=v;F=v=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)Cb(a,c[e]);null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),Db(a,f)}finally{kb.current=b,F=null,v=d}}
function Q(a,b){l=new Uint8Array(512);n=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,p(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)p(b,e[c]);e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)a.pendingChunks--,p(b,f[c]);f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)a.pendingChunks--,p(b,g[c]);g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<n&&(b.enqueue(new Uint8Array(l.buffer,0,n)),l=null,n=0)}0===a.pendingChunks&&
(lb(a),b.close())}function Eb(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===C)vb(a,b.message),wb(a,c,b);else{var e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=O(a,e);P(a,c,f,e)}d.forEach(function(g){g.status=3;var h=S(c);g=Ab(a,g.id,h);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),Db(a,g)}}
function qb(a){if(a){var b=D;Ia(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];if(!eb[e]){var f={$$typeof:xa,_currentValue:B,_currentValue2:B,_defaultValue:B,_threadCount:0,Provider:null,Consumer:null,_globalName:e};f.Provider={$$typeof:wa,_context:f};eb[e]=f}Ja(eb[e],c)}a=D;Ia(b);return a}return null}
function Fb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var Gb=new Map;
function Hb(a){var b=__webpack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function Ib(){}
function Jb(a){for(var b=a[1],d=[],c=0;c<b.length;){var e=b[c++],f=b[c++],g=Gb.get(e);void 0===g?(Kb.set(e,f),f=__webpack_chunk_load__(e),d.push(f),g=Gb.set.bind(Gb,e,null),f.then(g,Ib),Gb.set(e,f)):null!==g&&d.push(g)}return 4===a.length?0===d.length?Hb(a[0]):Promise.all(d).then(function(){return Hb(a[0])}):0<d.length?Promise.all(d):null}
function W(a){var b=__webpack_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}var Kb=new Map,Lb=__webpack_require__.u;__webpack_require__.u=function(a){var b=Kb.get(a);return void 0!==b?b:Lb(a)};function Mb(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Mb.prototype=Object.create(Promise.prototype);
Mb.prototype.then=function(a,b){switch(this.status){case "resolved_model":Nb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Ob(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Pb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Ob(d,b)}}function Qb(a,b,d,c,e,f){var g=Fb(a._bundlerConfig,b);a=Jb(g);if(d)d=Promise.all([d,a]).then(function(h){h=h[0];var k=W(g);return k.bind.apply(k,[null].concat(h))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Rb(c,e,f),Sb(c));return null}var X=null,Y=null;
function Nb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Tb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Pb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Mb("resolved_model",c,null,a):new Mb("pending",null,null,a),d.set(b,c));return c}function Rb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Ob(e,c.value))}}function Sb(a){return function(b){return Pb(a,b)}}
function Ub(a,b){a=Z(a,b);"resolved_model"===a.status&&Nb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Zb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Ub(a,c),Qb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Ub(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Ub(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,h){h.startsWith(e)&&f.append(h.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Nb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Rb(c,b,d),Sb(c)),null;default:throw a.reason;}}return c}
function $b(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Zb(e,this,f,g):g}};return e}function ac(a){Tb(a,Error("Connection closed."))}function bc(a,b,d){var c=Fb(a,b);a=Jb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}
function cc(a,b,d){a=$b(b,d,a);ac(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=u({},a,!1);return new Proxy(a,ka)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=cc(a,b,e),c=bc(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=bc(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};
exports.decodeFormState=function(a,b,d){var c=b.get("$ACTION_KEY");if("string"!==typeof c)return Promise.resolve(null);var e=null;b.forEach(function(g,h){h.startsWith("$ACTION_REF_")&&(g="$ACTION_"+h.slice(12)+":",e=cc(b,d,g))});if(null===e)return Promise.resolve(null);var f=e.id;return Promise.resolve(e.bound).then(function(g){return null===g?null:[a,c,f,g.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=$b(b,"",a);ac(a);return Z(a,0)};
exports.registerClientReference=function(a,b,d){return u(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=ob(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)Eb(c,e.reason);else{var f=function(){Eb(c,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){c.flushScheduled=null!==c.destination;zb(c)},pull:function(g){if(1===c.status)c.status=2,ca(g,c.fatalError);else if(2!==c.status&&null===
c.destination){c.destination=g;try{Q(c,g)}catch(h){O(c,h),Db(c,h)}}},cancel:function(g){c.destination=null;Eb(c,g)}},{highWaterMark:0})};
