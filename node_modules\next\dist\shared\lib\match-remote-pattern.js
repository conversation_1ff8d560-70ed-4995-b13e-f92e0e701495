"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    matchRemotePattern: null,
    hasMatch: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    matchRemotePattern: function() {
        return matchRemotePattern;
    },
    hasMatch: function() {
        return hasMatch;
    }
});
const _micromatch = require("next/dist/compiled/micromatch");
function matchRemotePattern(pattern, url) {
    if (pattern.protocol !== undefined) {
        const actualProto = url.protocol.slice(0, -1);
        if (pattern.protocol !== actualProto) {
            return false;
        }
    }
    if (pattern.port !== undefined) {
        if (pattern.port !== url.port) {
            return false;
        }
    }
    if (pattern.hostname === undefined) {
        throw new Error("Pattern should define hostname but found\n" + JSON.stringify(pattern));
    } else {
        if (!(0, _micromatch.makeRe)(pattern.hostname).test(url.hostname)) {
            return false;
        }
    }
    var _pattern_pathname;
    if (!(0, _micromatch.makeRe)((_pattern_pathname = pattern.pathname) != null ? _pattern_pathname : "**").test(url.pathname)) {
        return false;
    }
    return true;
}
function hasMatch(domains, remotePatterns, url) {
    return domains.some((domain)=>url.hostname === domain) || remotePatterns.some((p)=>matchRemotePattern(p, url));
}

//# sourceMappingURL=match-remote-pattern.js.map