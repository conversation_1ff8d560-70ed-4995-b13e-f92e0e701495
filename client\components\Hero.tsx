export function Hero() {
  return (
    <section className="bg-crypto-dark min-h-screen flex items-center relative overflow-hidden">
      <div className="container mx-auto px-6 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            {/* Trust Badge */}
            <div className="inline-flex items-center bg-crypto-dark-lighter border border-neon-green/20 rounded-full px-4 py-2">
              <img
                src="https://cdn.builder.io/api/v1/image/assets%2F700ab04c03304effad4d5e385f1a769d%2Fb5f2b7dadbba4104880a531500d9fdf3?format=webp&width=800"
                alt="Trusted platform icon"
                className="w-3 h-3 mr-3"
              />
              <span className="text-white text-sm font-medium">100% TRUSTED PLATFORM</span>
            </div>

            {/* Main Headline */}
            <div className="space-y-4">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                POWERING GLOBAL
                <br />
                PAYMENTS WITH
                <br />
                <span className="text-neon-green">BLOCKCHAIN</span>
              </h1>
            </div>

            {/* Description */}
            <p className="text-gray-300 text-lg max-w-xl leading-relaxed">
              Praith Is A Blockchain-Powered Crypto Payment Gateway That 
              Solves The Traditional Payment Problems. Our Platform Enables Fast, 
              Low-Fee, And Borderless Transactions Using Stablecoins On The 
              Tron And Binance Smart Chain (BEP-20) Networks.
            </p>

            {/* CTA Button */}
            <button className="bg-neon-green text-crypto-dark px-8 py-4 rounded-lg font-bold text-lg hover:bg-neon-green-dark transition-colors shadow-lg shadow-neon-green/25">
              Get Started
            </button>
          </div>

          {/* Right Content - Globe Visualization */}
          <div className="relative flex items-center justify-center mt-8 lg:mt-0">
            <div className="relative w-96 h-96 sm:w-[28rem] sm:h-[28rem] lg:w-[32rem] lg:h-[32rem] mx-auto">
              {/* Globe Image */}
              <div className="relative w-full h-full">
                <img
                  src="https://cdn.builder.io/api/v1/image/assets%2F700ab04c03304effad4d5e385f1a769d%2Fbf31bb54612d44eaad1fbe3e0adc8317?format=webp&width=800"
                  alt="Global blockchain network"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-crypto-dark via-crypto-dark to-transparent opacity-90"></div>
    </section>
  );
}
