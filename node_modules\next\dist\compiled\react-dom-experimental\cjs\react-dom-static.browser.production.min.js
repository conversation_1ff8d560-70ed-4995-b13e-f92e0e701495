/**
 * @license React
 * react-dom-static.browser.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var ba=require("next/dist/compiled/react-experimental"),da=require("react-dom");function h(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var k=null,l=0;
function q(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<l&&(a.enqueue(new Uint8Array(k.buffer,0,l)),k=new Uint8Array(512),l=0),a.enqueue(b);else{var c=k.length-l;c<b.byteLength&&(0===c?a.enqueue(k):(k.set(b.subarray(0,c),l),a.enqueue(k),b=b.subarray(c)),k=new Uint8Array(512),l=0);k.set(b,l);l+=b.byteLength}}function r(a,b){q(a,b);return!0}function ia(a){k&&0<l&&(a.enqueue(new Uint8Array(k.buffer,0,l)),k=null,l=0)}var ra=new TextEncoder;function x(a){return ra.encode(a)}
function y(a){return ra.encode(a)}function sa(a,b){"function"===typeof a.error?a.error(b):a.close()}
var ta=Object.assign,z=Object.prototype.hasOwnProperty,ua=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),va={},Aa={};
function Ba(a){if(z.call(Aa,a))return!0;if(z.call(va,a))return!1;if(ua.test(a))return Aa[a]=!0;va[a]=!0;return!1}
var Ca=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Da=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ea=/["'&<>]/;
function A(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ea.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Fa=/([A-Z])/g,La=/^ms-/,Ma=Array.isArray,Na=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Oa={pending:!1,data:null,method:null,action:null},Pa=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,lb={prefetchDNS:Qa,preconnect:Ra,preload:hb,preloadModule:ib,preinit:jb,preinitModule:kb},mb=y('"></template>'),nb=y("<script>"),ub=y("\x3c/script>"),vb=y('<script src="'),wb=y('<script type="module" src="'),xb=y('" nonce="'),yb=y('" integrity="'),zb=y('" crossorigin="'),Ab=y('" async="">\x3c/script>'),
Bb=/(<\/|<)(s)(cript)/gi;function Cb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}
function Db(a,b,c,d,e,g,f){b=void 0===b?"":b;var m=void 0===c?nb:y('<script nonce="'+A(c)+'">'),n=[],t=null,v=0;void 0!==d&&n.push(m,x((""+d).replace(Bb,Cb)),ub);void 0!==f&&(v=1,"string"===typeof f?(t={src:f,chunks:[]},Eb(t.chunks,{src:f,async:!0,integrity:void 0,nonce:c})):(t={src:f.src,chunks:[]},Eb(t.chunks,{src:f.src,async:!0,integrity:f.integrity,nonce:c})));if(void 0!==e)for(d=0;d<e.length;d++){var p=e[d];f="string"===typeof p?p:p.src;var w="string"===typeof p?void 0:p.integrity;p="string"===
typeof p||null==p.crossOrigin?void 0:"use-credentials"===p.crossOrigin?"use-credentials":"";var H=a,I={rel:"preload",href:f,as:"script",fetchPriority:"low",nonce:c,integrity:w,crossOrigin:p},E={type:"preload",chunks:[],state:0,props:I};H.preloadsMap.set("[script]"+f,E);H.bootstrapScripts.add(E);F(E.chunks,I);n.push(vb,x(A(f)));c&&n.push(xb,x(A(c)));w&&n.push(yb,x(A(w)));"string"===typeof p&&n.push(zb,x(A(p)));n.push(Ab)}if(void 0!==g)for(e=0;e<g.length;e++)w=g[e],d="string"===typeof w?w:w.src,f="string"===
typeof w?void 0:w.integrity,w="string"===typeof w||null==w.crossOrigin?void 0:"use-credentials"===w.crossOrigin?"use-credentials":"",p=a,H={rel:"modulepreload",href:d,fetchPriority:"low",nonce:c,integrity:f,crossOrigin:w},I={type:"preload",chunks:[],state:0,props:H},p.preloadsMap.set("[script]"+d,I),p.bootstrapScripts.add(I),F(I.chunks,H),n.push(wb,x(A(d))),c&&n.push(xb,x(A(c))),f&&n.push(yb,x(A(f))),"string"===typeof w&&n.push(zb,x(A(w))),n.push(Ab);return{bootstrapChunks:n,placeholderPrefix:y(b+
"P:"),segmentPrefix:y(b+"S:"),boundaryPrefix:b+"B:",idPrefix:b,nextSuspenseID:0,streamingFormat:v,startInlineScript:m,instructions:0,externalRuntimeScript:t,htmlChunks:null,headChunks:null,hasBody:!1,charsetChunks:[],preconnectChunks:[],preloadChunks:[],hoistableChunks:[],stylesToHoist:!1,nonce:c}}function G(a,b,c){return{insertionMode:a,selectedValue:b,noscriptTagInScope:c}}function Fb(a){return G("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,!1)}
function Gb(a,b,c){switch(b){case "noscript":return G(2,null,!0);case "select":return G(2,null!=c.value?c.value:c.defaultValue,a.noscriptTagInScope);case "svg":return G(3,null,a.noscriptTagInScope);case "math":return G(4,null,a.noscriptTagInScope);case "foreignObject":return G(2,null,a.noscriptTagInScope);case "table":return G(5,null,a.noscriptTagInScope);case "thead":case "tbody":case "tfoot":return G(6,null,a.noscriptTagInScope);case "colgroup":return G(8,null,a.noscriptTagInScope);case "tr":return G(7,
null,a.noscriptTagInScope)}return 5<=a.insertionMode?G(2,null,a.noscriptTagInScope):0===a.insertionMode?"html"===b?G(1,null,!1):G(2,null,!1):1===a.insertionMode?G(2,null,!1):a}var Hb=y("\x3c!-- --\x3e");function Ib(a,b,c,d){if(""===b)return d;d&&a.push(Hb);a.push(x(A(b)));return!0}var Jb=new Map,Kb=y(' style="'),Lb=y(":"),Mb=y(";");
function Nb(a,b){if("object"!==typeof b)throw Error(h(62));var c=!0,d;for(d in b)if(z.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var g=x(A(d));e=x(A((""+e).trim()))}else g=Jb.get(d),void 0===g&&(g=y(A(d.replace(Fa,"-$1").toLowerCase().replace(La,"-ms-"))),Jb.set(d,g)),e="number"===typeof e?0===e||Ca.has(d)?x(""+e):x(e+"px"):x(A((""+e).trim()));c?(c=!1,a.push(Kb,g,Lb,e)):a.push(Mb,g,Lb,e)}}c||a.push(J)}var K=y(" "),M=y('="'),J=y('"'),Ob=y('=""');
function Pb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(K,x(b),Ob)}function N(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(K,x(b),M,x(A(c)),J)}function Qb(a){var b=a.nextSuspenseID++;return a.idPrefix+b}var Rb=y(A("javascript:throw new Error('A React form was unexpectedly submitted.')")),Sb=y('<input type="hidden"');function Tb(a,b){this.push(Sb);if("string"!==typeof a)throw Error(h(480));N(this,"name",b);N(this,"value",a);this.push(Ub)}
function ac(a,b,c,d,e,g,f){var m=null;"function"===typeof c&&("function"===typeof c.$$FORM_ACTION?(d=Qb(b),b=c.$$FORM_ACTION(d),f=b.name,c=b.action||"",d=b.encType,e=b.method,g=b.target,m=b.data):(a.push(K,x("formAction"),M,Rb,J),g=e=d=c=f=null,bc(b)));null!=f&&O(a,"name",f);null!=c&&O(a,"formAction",c);null!=d&&O(a,"formEncType",d);null!=e&&O(a,"formMethod",e);null!=g&&O(a,"formTarget",g);return m}
function O(a,b,c){switch(b){case "className":N(a,"class",c);break;case "tabIndex":N(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":N(a,b,c);break;case "style":Nb(a,c);break;case "src":case "href":if(""===c)break;case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(K,x(b),M,x(A(c)),J);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Pb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(K,x("xlink:href"),M,x(A(c)),J);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(K,x(b),M,x(A(c)),J);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(K,x(b),Ob);break;case "capture":case "download":!0===c?a.push(K,x(b),Ob):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(K,x(b),M,x(A(c)),J);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(K,x(b),M,x(A(c)),J);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(K,x(b),M,x(A(c)),J);break;case "xlinkActuate":N(a,"xlink:actuate",
c);break;case "xlinkArcrole":N(a,"xlink:arcrole",c);break;case "xlinkRole":N(a,"xlink:role",c);break;case "xlinkShow":N(a,"xlink:show",c);break;case "xlinkTitle":N(a,"xlink:title",c);break;case "xlinkType":N(a,"xlink:type",c);break;case "xmlBase":N(a,"xml:base",c);break;case "xmlLang":N(a,"xml:lang",c);break;case "xmlSpace":N(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Da.get(b)||b,Ba(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(K,x(b),M,x(A(c)),J)}}}var T=y(">"),Ub=y("/>");function cc(a,b,c){if(null!=b){if(null!=c)throw Error(h(60));if("object"!==typeof b||!("__html"in b))throw Error(h(61));b=b.__html;null!==b&&void 0!==b&&a.push(x(""+b))}}function dc(a){var b="";ba.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var ec=y(' selected=""'),fc=y('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});');
function bc(a){0!==(a.instructions&16)||a.externalRuntimeScript||(a.instructions|=16,a.bootstrapChunks.unshift(a.startInlineScript,fc,ub))}
function gc(a,b,c,d,e,g,f){var m=b.rel,n=b.href,t=b.precedence;if(3===g||f||null!=b.itemProp||"string"!==typeof m||"string"!==typeof n||""===n)return F(a,b),null;if("stylesheet"===b.rel){c="[style]"+n;if("string"!==typeof t||null!=b.disabled||b.onLoad||b.onError)return F(a,b);g=d.stylesMap.get(c);g||(b=ta({},b,{"data-precedence":b.precedence,precedence:null}),g=d.preloadsMap.get(c),f=0,g&&(g.state|=4,m=g.props,null==b.crossOrigin&&(b.crossOrigin=m.crossOrigin),null==b.integrity&&(b.integrity=m.integrity),
g.state&3&&(f=8)),g={type:"stylesheet",chunks:[],state:f,props:b},d.stylesMap.set(c,g),b=d.precedences.get(t),b||(b=new Set,d.precedences.set(t,b),c={type:"style",chunks:[],state:0,props:{precedence:t,hrefs:[]}},b.add(c),d.stylePrecedences.set(t,c)),b.add(g));d.boundaryResources&&d.boundaryResources.add(g);e&&a.push(Hb);return null}if(b.onLoad||b.onError)return F(a,b);e&&a.push(Hb);switch(b.rel){case "preconnect":case "dns-prefetch":return F(c.preconnectChunks,b);case "preload":return F(c.preloadChunks,
b);default:return F(c.hoistableChunks,b)}}function F(a,b){a.push(U("link"));for(var c in b)if(z.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error(h(399,"link"));default:O(a,c,d)}}a.push(Ub);return null}function hc(a,b,c){var d="";"string"===typeof b&&""!==b?(d+="["+b+"]","string"===typeof c&&(d+="["+c+"]")):d+="[][]"+a;return"[image]"+d}
function ic(a,b,c){a.push(U(c));for(var d in b)if(z.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(h(399,c));default:O(a,d,e)}}a.push(Ub);return null}
function jc(a,b){a.push(U("title"));var c=null,d=null,e;for(e in b)if(z.call(b,e)){var g=b[e];if(null!=g)switch(e){case "children":c=g;break;case "dangerouslySetInnerHTML":d=g;break;default:O(a,e,g)}}a.push(T);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(x(A(""+b)));cc(a,d,c);a.push(kc,x("title"),lc);return null}
function Eb(a,b){a.push(U("script"));var c=null,d=null,e;for(e in b)if(z.call(b,e)){var g=b[e];if(null!=g)switch(e){case "children":c=g;break;case "dangerouslySetInnerHTML":d=g;break;default:O(a,e,g)}}a.push(T);cc(a,d,c);"string"===typeof c&&a.push(x(A(c)));a.push(kc,x("script"),lc);return null}
function mc(a,b,c){a.push(U(c));var d=c=null,e;for(e in b)if(z.call(b,e)){var g=b[e];if(null!=g)switch(e){case "children":c=g;break;case "dangerouslySetInnerHTML":d=g;break;default:O(a,e,g)}}a.push(T);cc(a,d,c);return"string"===typeof c?(a.push(x(A(c))),null):c}var nc=y("\n"),oc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,pc=new Map;function U(a){var b=pc.get(a);if(void 0===b){if(!oc.test(a))throw Error(h(65,a));b=y("<"+a);pc.set(a,b)}return b}var qc=y("<!DOCTYPE html>");
function rc(a,b,c,d,e,g,f){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(U("select"));var m=null,n=null,t;for(t in c)if(z.call(c,t)){var v=c[t];if(null!=v)switch(t){case "children":m=v;break;case "dangerouslySetInnerHTML":n=v;break;case "defaultValue":case "value":break;default:O(a,t,v)}}a.push(T);cc(a,n,m);return m;case "option":var p=g.selectedValue;a.push(U("option"));var w=null,H=null,I=null,E=null,u;for(u in c)if(z.call(c,
u)){var P=c[u];if(null!=P)switch(u){case "children":w=P;break;case "selected":I=P;break;case "dangerouslySetInnerHTML":E=P;break;case "value":H=P;default:O(a,u,P)}}if(null!=p){var Q=null!==H?""+H:dc(w);if(Ma(p))for(var L=0;L<p.length;L++){if(""+p[L]===Q){a.push(ec);break}}else""+p===Q&&a.push(ec)}else I&&a.push(ec);a.push(T);cc(a,E,w);return w;case "textarea":a.push(U("textarea"));var B=null,R=null,C=null,D;for(D in c)if(z.call(c,D)){var ja=c[D];if(null!=ja)switch(D){case "children":C=ja;break;case "value":B=
ja;break;case "defaultValue":R=ja;break;case "dangerouslySetInnerHTML":throw Error(h(91));default:O(a,D,ja)}}null===B&&null!==R&&(B=R);a.push(T);if(null!=C){if(null!=B)throw Error(h(92));if(Ma(C)&&1<C.length)throw Error(h(93));B=""+C}"string"===typeof B&&"\n"===B[0]&&a.push(nc);null!==B&&a.push(x(A(""+B)));return null;case "input":a.push(U("input"));var Ga=null,wa=null,ka=null,ea=null,xa=null,la=null,ma=null,na=null,Ha=null,ca;for(ca in c)if(z.call(c,ca)){var aa=c[ca];if(null!=aa)switch(ca){case "children":case "dangerouslySetInnerHTML":throw Error(h(399,
"input"));case "name":Ga=aa;break;case "formAction":wa=aa;break;case "formEncType":ka=aa;break;case "formMethod":ea=aa;break;case "formTarget":xa=aa;break;case "defaultChecked":Ha=aa;break;case "defaultValue":ma=aa;break;case "checked":na=aa;break;case "value":la=aa;break;default:O(a,ca,aa)}}var Oc=ac(a,e,wa,ka,ea,xa,Ga);null!==na?Pb(a,"checked",na):null!==Ha&&Pb(a,"checked",Ha);null!==la?O(a,"value",la):null!==ma&&O(a,"value",ma);a.push(Ub);null!==Oc&&Oc.forEach(Tb,a);return null;case "button":a.push(U("button"));
var Sa=null,Pc=null,Qc=null,Rc=null,Sc=null,Tc=null,Uc=null,Ta;for(Ta in c)if(z.call(c,Ta)){var fa=c[Ta];if(null!=fa)switch(Ta){case "children":Sa=fa;break;case "dangerouslySetInnerHTML":Pc=fa;break;case "name":Qc=fa;break;case "formAction":Rc=fa;break;case "formEncType":Sc=fa;break;case "formMethod":Tc=fa;break;case "formTarget":Uc=fa;break;default:O(a,Ta,fa)}}var Vc=ac(a,e,Rc,Sc,Tc,Uc,Qc);a.push(T);null!==Vc&&Vc.forEach(Tb,a);cc(a,Pc,Sa);if("string"===typeof Sa){a.push(x(A(Sa)));var Wc=null}else Wc=
Sa;return Wc;case "form":a.push(U("form"));var Ua=null,Xc=null,oa=null,Va=null,Wa=null,Xa=null,Ya;for(Ya in c)if(z.call(c,Ya)){var pa=c[Ya];if(null!=pa)switch(Ya){case "children":Ua=pa;break;case "dangerouslySetInnerHTML":Xc=pa;break;case "action":oa=pa;break;case "encType":Va=pa;break;case "method":Wa=pa;break;case "target":Xa=pa;break;default:O(a,Ya,pa)}}var Vb=null,Wb=null;if("function"===typeof oa)if("function"===typeof oa.$$FORM_ACTION){var Me=Qb(e),Ia=oa.$$FORM_ACTION(Me);oa=Ia.action||"";Va=
Ia.encType;Wa=Ia.method;Xa=Ia.target;Vb=Ia.data;Wb=Ia.name}else a.push(K,x("action"),M,Rb,J),Xa=Wa=Va=oa=null,bc(e);null!=oa&&O(a,"action",oa);null!=Va&&O(a,"encType",Va);null!=Wa&&O(a,"method",Wa);null!=Xa&&O(a,"target",Xa);a.push(T);null!==Wb&&(a.push(Sb),N(a,"name",Wb),a.push(Ub),null!==Vb&&Vb.forEach(Tb,a));cc(a,Xc,Ua);if("string"===typeof Ua){a.push(x(A(Ua)));var Yc=null}else Yc=Ua;return Yc;case "menuitem":a.push(U("menuitem"));for(var ob in c)if(z.call(c,ob)){var Zc=c[ob];if(null!=Zc)switch(ob){case "children":case "dangerouslySetInnerHTML":throw Error(h(400));
default:O(a,ob,Zc)}}a.push(T);return null;case "title":if(3===g.insertionMode||g.noscriptTagInScope||null!=c.itemProp)var $c=jc(a,c);else jc(e.hoistableChunks,c),$c=null;return $c;case "link":return gc(a,c,e,d,f,g.insertionMode,g.noscriptTagInScope);case "script":var Xb=c.async;if("string"!==typeof c.src||!c.src||!Xb||"function"===typeof Xb||"symbol"===typeof Xb||c.onLoad||c.onError||3===g.insertionMode||g.noscriptTagInScope||null!=c.itemProp)var ad=Eb(a,c);else{var Yb="[script]"+c.src,Za=d.scriptsMap.get(Yb);
if(!Za){Za={type:"script",chunks:[],state:0,props:null};d.scriptsMap.set(Yb,Za);d.scripts.add(Za);var bd=c,pb=d.preloadsMap.get(Yb);if(pb){pb.state|=4;var qb=bd=ta({},c),cd=pb.props;null==qb.crossOrigin&&(qb.crossOrigin=cd.crossOrigin);null==qb.integrity&&(qb.integrity=cd.integrity)}Eb(Za.chunks,bd)}f&&a.push(Hb);ad=null}return ad;case "style":var $a=c.precedence,ab=c.href;if(3===g.insertionMode||g.noscriptTagInScope||null!=c.itemProp||"string"!==typeof $a||"string"!==typeof ab||""===ab){a.push(U("style"));
var Ja=null,dd=null,bb;for(bb in c)if(z.call(c,bb)){var rb=c[bb];if(null!=rb)switch(bb){case "children":Ja=rb;break;case "dangerouslySetInnerHTML":dd=rb;break;default:O(a,bb,rb)}}a.push(T);var cb=Array.isArray(Ja)?2>Ja.length?Ja[0]:null:Ja;"function"!==typeof cb&&"symbol"!==typeof cb&&null!==cb&&void 0!==cb&&a.push(x(A(""+cb)));cc(a,dd,Ja);a.push(kc,x("style"),lc);var ed=null}else{var fd="[style]"+ab,ha=d.stylesMap.get(fd);if(!ha){if(ha=d.stylePrecedences.get($a))ha.props.hrefs.push(ab);else{ha={type:"style",
chunks:[],state:0,props:{precedence:$a,hrefs:[ab]}};d.stylePrecedences.set($a,ha);var gd=new Set;gd.add(ha);d.precedences.set($a,gd)}d.stylesMap.set(fd,ha);d.boundaryResources&&d.boundaryResources.add(ha);var hd=ha.chunks,Ka=null,id=null,sb;for(sb in c)if(z.call(c,sb)){var Zb=c[sb];if(null!=Zb)switch(sb){case "children":Ka=Zb;break;case "dangerouslySetInnerHTML":id=Zb}}var db=Array.isArray(Ka)?2>Ka.length?Ka[0]:null:Ka;"function"!==typeof db&&"symbol"!==typeof db&&null!==db&&void 0!==db&&hd.push(x(A(""+
db)));cc(hd,id,Ka)}f&&a.push(Hb);ed=void 0}return ed;case "meta":if(3===g.insertionMode||g.noscriptTagInScope||null!=c.itemProp)var jd=ic(a,c,"meta");else f&&a.push(Hb),jd="string"===typeof c.charSet?ic(e.charsetChunks,c,"meta"):"viewport"===c.name?ic(e.preconnectChunks,c,"meta"):ic(e.hoistableChunks,c,"meta");return jd;case "listing":case "pre":a.push(U(b));var eb=null,fb=null,gb;for(gb in c)if(z.call(c,gb)){var tb=c[gb];if(null!=tb)switch(gb){case "children":eb=tb;break;case "dangerouslySetInnerHTML":fb=
tb;break;default:O(a,gb,tb)}}a.push(T);if(null!=fb){if(null!=eb)throw Error(h(60));if("object"!==typeof fb||!("__html"in fb))throw Error(h(61));var ya=fb.__html;null!==ya&&void 0!==ya&&("string"===typeof ya&&0<ya.length&&"\n"===ya[0]?a.push(nc,x(ya)):a.push(x(""+ya)))}"string"===typeof eb&&"\n"===eb[0]&&a.push(nc);return eb;case "img":var W=c.src,S=c.srcSet;if("lazy"!==c.loading&&("string"===typeof W||"string"===typeof S)&&"low"!==c.fetchPriority&&("string"!==typeof W||":"!==W[4]||"d"!==W[0]&&"D"!==
W[0]||"a"!==W[1]&&"A"!==W[1]||"t"!==W[2]&&"T"!==W[2]||"a"!==W[3]&&"A"!==W[3])&&("string"!==typeof S||":"!==S[4]||"d"!==S[0]&&"D"!==S[0]||"a"!==S[1]&&"A"!==S[1]||"t"!==S[2]&&"T"!==S[2]||"a"!==S[3]&&"A"!==S[3])){var kd=c.sizes,ld=hc(W,S,kd),za=d.preloadsMap.get(ld);za||(za={type:"preload",chunks:[],state:0,props:{rel:"preload",as:"image",href:S?void 0:W,imageSrcSet:S,imageSizes:kd,crossOrigin:c.crossOrigin,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}},
d.preloadsMap.set(ld,za),F(za.chunks,za.props));"high"===c.fetchPriority||10>d.highImagePreloads.size?d.highImagePreloads.add(za):d.bulkPreloads.add(za)}return ic(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return ic(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>
g.insertionMode&&null===e.headChunks){e.headChunks=[];var md=mc(e.headChunks,c,"head")}else md=mc(a,c,"head");return md;case "html":if(0===g.insertionMode&&null===e.htmlChunks){e.htmlChunks=[qc];var nd=mc(e.htmlChunks,c,"html")}else nd=mc(a,c,"html");return nd;default:if(-1!==b.indexOf("-")){a.push(U(b));var $b=null,od=null,qa;for(qa in c)if(z.call(c,qa)){var Y=c[qa];if(null!=Y&&"function"!==typeof Y&&"object"!==typeof Y&&!1!==Y)switch(!0===Y&&(Y=""),"className"===qa&&(qa="class"),qa){case "children":$b=
Y;break;case "dangerouslySetInnerHTML":od=Y;break;case "style":Nb(a,Y);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:Ba(qa)&&"function"!==typeof Y&&"symbol"!==typeof Y&&a.push(K,x(qa),M,x(A(Y)),J)}}a.push(T);cc(a,od,$b);return $b}}return mc(a,c,b)}var kc=y("</"),lc=y(">");function sc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)q(a,b[c]);return c<b.length?(c=b[c],b.length=0,r(a,c)):!0}
var tc=y('<template id="'),uc=y('"></template>'),vc=y("\x3c!--$--\x3e"),wc=y('\x3c!--$?--\x3e<template id="'),xc=y('"></template>'),yc=y("\x3c!--$!--\x3e"),zc=y("\x3c!--/$--\x3e"),Ac=y("<template"),Bc=y('"'),Cc=y(' data-dgst="');y(' data-msg="');y(' data-stck="');var Dc=y("></template>");function Ec(a,b,c){q(a,wc);if(null===c)throw Error(h(395));q(a,c);return r(a,xc)}
var Fc=y('<div hidden id="'),Gc=y('">'),Hc=y("</div>"),Ic=y('<svg aria-hidden="true" style="display:none" id="'),Jc=y('">'),Kc=y("</svg>"),Lc=y('<math aria-hidden="true" style="display:none" id="'),Mc=y('">'),Nc=y("</math>"),pd=y('<table hidden id="'),qd=y('">'),rd=y("</table>"),sd=y('<table hidden><tbody id="'),td=y('">'),ud=y("</tbody></table>"),vd=y('<table hidden><tr id="'),wd=y('">'),xd=y("</tr></table>"),yd=y('<table hidden><colgroup id="'),zd=y('">'),Ad=y("</colgroup></table>");
function Bd(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return q(a,Fc),q(a,b.segmentPrefix),q(a,x(d.toString(16))),r(a,Gc);case 3:return q(a,Ic),q(a,b.segmentPrefix),q(a,x(d.toString(16))),r(a,Jc);case 4:return q(a,Lc),q(a,b.segmentPrefix),q(a,x(d.toString(16))),r(a,Mc);case 5:return q(a,pd),q(a,b.segmentPrefix),q(a,x(d.toString(16))),r(a,qd);case 6:return q(a,sd),q(a,b.segmentPrefix),q(a,x(d.toString(16))),r(a,td);case 7:return q(a,vd),q(a,b.segmentPrefix),q(a,x(d.toString(16))),r(a,wd);
case 8:return q(a,yd),q(a,b.segmentPrefix),q(a,x(d.toString(16))),r(a,zd);default:throw Error(h(397));}}function Cd(a,b){switch(b.insertionMode){case 0:case 1:case 2:return r(a,Hc);case 3:return r(a,Kc);case 4:return r(a,Nc);case 5:return r(a,rd);case 6:return r(a,ud);case 7:return r(a,xd);case 8:return r(a,Ad);default:throw Error(h(397));}}
var Dd=y('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};;$RS("'),Ed=y('$RS("'),Fd=y('","'),Gd=y('")\x3c/script>'),Hd=y('<template data-rsi="" data-sid="'),Id=y('" data-pid="'),Jd=y('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
Kd=y('$RC("'),Ld=y('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Md=y('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Nd=y('$RR("'),Od=y('","'),Pd=y('",'),Qd=y('"'),Rd=y(")\x3c/script>"),Sd=y('<template data-rci="" data-bid="'),Td=y('<template data-rri="" data-bid="'),Ud=y('" data-sid="'),Vd=y('" data-sty="'),Wd=y('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),Xd=y('$RX("'),Yd=y('"'),Zd=y(","),$d=y(")\x3c/script>"),ae=y('<template data-rxi="" data-bid="'),be=y('" data-dgst="'),
ce=y('" data-msg="'),de=y('" data-stck="'),ee=/[<\u2028\u2029]/g;function fe(a){return JSON.stringify(a).replace(ee,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var ge=/[&><\u2028\u2029]/g;
function he(a){return JSON.stringify(a).replace(ge,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var ie=y('<style media="not all" data-precedence="'),je=y('" data-href="'),ke=y('">'),le=y("</style>"),me=!1,ne=!0;function oe(a){if("stylesheet"===a.type&&0===(a.state&1))me=!0;else if("style"===a.type){var b=a.chunks,c=a.props.hrefs,d=0;if(b.length){q(this,ie);q(this,x(A(a.props.precedence)));if(c.length){for(q(this,je);d<c.length-1;d++)q(this,x(A(c[d]))),q(this,pe);q(this,x(A(c[d])))}q(this,ke);for(d=0;d<b.length;d++)q(this,b[d]);ne=r(this,le);me=!0;b.length=0;c.length=0}}}
function qe(a,b,c){me=!1;ne=!0;b.forEach(oe,a);me&&(c.stylesToHoist=!0);return ne}function re(a){if(0===(a.state&7)){for(var b=a.chunks,c=0;c<b.length;c++)q(this,b[c]);a.state|=1}}function se(a){if(0===(a.state&7)){for(var b=a.chunks,c=0;c<b.length;c++)q(this,b[c]);a.state|=2}}var te=null,ue=!1;function ve(a,b,c){b=a.chunks;if(a.state&3)c.delete(a);else if("style"===a.type)te=a;else{F(b,a.props);for(c=0;c<b.length;c++)q(this,b[c]);a.state|=1;ue=!0}}
var we=y('<style data-precedence="'),xe=y('" data-href="'),pe=y(" "),ye=y('">'),ze=y("</style>");function Ae(a,b){ue=!1;a.forEach(ve,this);a.clear();a=te.chunks;var c=te.props.hrefs;if(!1===ue||a.length){q(this,we);q(this,x(A(b)));b=0;if(c.length){for(q(this,xe);b<c.length-1;b++)q(this,x(A(c[b]))),q(this,pe);q(this,x(A(c[b])))}q(this,ye);for(b=0;b<a.length;b++)q(this,a[b]);q(this,ze);a.length=0;c.length=0}}
function Be(a){if(!(a.state&8)&&"style"!==a.type){var b=a.chunks,c=a.props;F(b,{rel:"preload",as:"style",href:a.props.href,crossOrigin:c.crossOrigin,fetchPriority:c.fetchPriority,integrity:c.integrity,media:c.media,hrefLang:c.hrefLang,referrerPolicy:c.referrerPolicy});for(c=0;c<b.length;c++)q(this,b[c]);a.state|=8;b.length=0}}function Ce(a){a.forEach(Be,this);a.clear()}var De=y("["),Ee=y(",["),Fe=y(","),Ge=y("]");
function He(a,b){q(a,De);var c=De;b.forEach(function(d){if("style"!==d.type&&!(d.state&1))if(d.state&3)q(a,c),q(a,x(he(""+d.props.href))),q(a,Ge),c=Ee;else if("stylesheet"===d.type){q(a,c);var e=d.props["data-precedence"],g=d.props;q(a,x(he(""+d.props.href)));e=""+e;q(a,Fe);q(a,x(he(e)));for(var f in g)if(z.call(g,f)){var m=g[f];if(null!=m)switch(f){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(h(399,"link"));default:a:{e=
a;var n=f.toLowerCase();switch(typeof m){case "function":case "symbol":break a}switch(f){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":n="class";m=""+m;break;case "hidden":if(!1===m)break a;m="";break;case "src":case "href":m=""+m;break;default:if(2<f.length&&("o"===f[0]||"O"===f[0])&&("n"===f[1]||"N"===f[1])||!Ba(f))break a;m=""+m}q(e,Fe);q(e,x(he(n)));q(e,Fe);q(e,x(he(m)))}}}q(a,Ge);c=Ee;
d.state|=2}});q(a,Ge)}
function Ie(a,b){q(a,De);var c=De;b.forEach(function(d){if("style"!==d.type&&!(d.state&1))if(d.state&3)q(a,c),q(a,x(A(JSON.stringify(""+d.props.href)))),q(a,Ge),c=Ee;else if("stylesheet"===d.type){q(a,c);var e=d.props["data-precedence"],g=d.props;q(a,x(A(JSON.stringify(""+d.props.href))));e=""+e;q(a,Fe);q(a,x(A(JSON.stringify(e))));for(var f in g)if(z.call(g,f)){var m=g[f];if(null!=m)switch(f){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(h(399,"link"));
default:a:{e=a;var n=f.toLowerCase();switch(typeof m){case "function":case "symbol":break a}switch(f){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":n="class";m=""+m;break;case "hidden":if(!1===m)break a;m="";break;case "src":case "href":m=""+m;break;default:if(2<f.length&&("o"===f[0]||"O"===f[0])&&("n"===f[1]||"N"===f[1])||!Ba(f))break a;m=""+m}q(e,Fe);q(e,x(A(JSON.stringify(n))));q(e,Fe);
q(e,x(A(JSON.stringify(m))))}}}q(a,Ge);c=Ee;d.state|=2}});q(a,Ge)}function Qa(a){var b=V?V:null;if(b){var c=b.resources;if("string"===typeof a&&a){var d="[prefetchDNS]"+a,e=c.preconnectsMap.get(d);e||(e={type:"preconnect",chunks:[],state:0,props:null},c.preconnectsMap.set(d,e),F(e.chunks,{href:a,rel:"dns-prefetch"}));c.preconnects.add(e);Je(b)}}}
function Ra(a,b){var c=V?V:null;if(c){var d=c.resources;if("string"===typeof a&&a){b=null==b||"string"!==typeof b.crossOrigin?null:"use-credentials"===b.crossOrigin?"use-credentials":"";var e="[preconnect]["+(null===b?"null":b)+"]"+a,g=d.preconnectsMap.get(e);g||(g={type:"preconnect",chunks:[],state:0,props:null},d.preconnectsMap.set(e,g),F(g.chunks,{rel:"preconnect",href:a,crossOrigin:b}));d.preconnects.add(g);Je(c)}}}
function hb(a,b){var c=V?V:null;if(c){var d=c.resources;if("string"===typeof a&&a&&"object"===typeof b&&null!==b&&"string"===typeof b.as&&b.as){var e=b.as;var g="image"===e?hc(a,b.imageSrcSet,b.imageSizes):"["+e+"]"+a;var f=d.preloadsMap.get(g);f||(f={type:"preload",chunks:[],state:0,props:{rel:"preload",as:e,href:"image"===e&&b.imageSrcSet?void 0:a,crossOrigin:"font"===e?"":b.crossOrigin,integrity:b.integrity,type:b.type,nonce:b.nonce,fetchPriority:b.fetchPriority,imageSrcSet:b.imageSrcSet,imageSizes:b.imageSizes,
referrerPolicy:b.referrerPolicy}},d.preloadsMap.set(g,f),F(f.chunks,f.props));"font"===e?d.fontPreloads.add(f):"image"===e&&"high"===b.fetchPriority?d.highImagePreloads.add(f):d.bulkPreloads.add(f);Je(c)}}}
function ib(a,b){var c=V?V:null;if(c){var d=c.resources;if("string"===typeof a&&a){var e=b&&"string"===typeof b.as?b.as:"script",g="["+e+"]"+a,f=d.preloadsMap.get(g);f||(f={type:"preload",chunks:[],state:0,props:{rel:"modulepreload",as:"script"!==e?e:void 0,href:a,crossOrigin:b?b.crossOrigin:void 0,integrity:b?b.integrity:void 0}},d.preloadsMap.set(g,f),F(f.chunks,f.props));d.bulkPreloads.add(f);Je(c)}}}
function jb(a,b){var c=V?V:null;if(c){var d=c.resources;if("string"===typeof a&&a&&"object"===typeof b&&null!==b){var e=b.as;switch(e){case "style":var g="["+e+"]"+a,f=d.stylesMap.get(g);e=b.precedence||"default";if(!f){f=0;var m=d.preloadsMap.get(g);m&&m.state&3&&(f=8);f={type:"stylesheet",chunks:[],state:f,props:{rel:"stylesheet",href:a,"data-precedence":e,crossOrigin:b.crossOrigin,integrity:b.integrity,fetchPriority:b.fetchPriority}};d.stylesMap.set(g,f);a=d.precedences.get(e);a||(a=new Set,d.precedences.set(e,
a),b={type:"style",chunks:[],state:0,props:{precedence:e,hrefs:[]}},a.add(b),d.stylePrecedences.set(e,b));a.add(f);Je(c)}break;case "script":g="["+e+"]"+a,e=d.scriptsMap.get(g),e||(e={type:"script",chunks:[],state:0,props:null},d.scriptsMap.set(g,e),a={src:a,async:!0,crossOrigin:b.crossOrigin,integrity:b.integrity,nonce:b.nonce,fetchPriority:b.fetchPriority},d.scripts.add(e),Eb(e.chunks,a),Je(c))}}}}
function kb(a,b){var c=V?V:null;if(c){var d=c.resources;if("string"===typeof a&&a){var e=b&&"string"===typeof b.as?b.as:"script";switch(e){case "script":var g="["+e+"]"+a;e=d.scriptsMap.get(g);e||(e={type:"script",chunks:[],state:0,props:null},d.scriptsMap.set(g,e),a={src:a,type:"module",async:!0,crossOrigin:b?b.crossOrigin:void 0,integrity:b?b.integrity:void 0},d.scripts.add(e),Eb(e.chunks,a),Je(c))}}}}function Ke(a){this.add(a)}
var Le=Symbol.for("react.element"),Ne=Symbol.for("react.portal"),Oe=Symbol.for("react.fragment"),Pe=Symbol.for("react.strict_mode"),Qe=Symbol.for("react.profiler"),Re=Symbol.for("react.provider"),Se=Symbol.for("react.context"),Te=Symbol.for("react.server_context"),Ue=Symbol.for("react.forward_ref"),Ve=Symbol.for("react.suspense"),We=Symbol.for("react.suspense_list"),Xe=Symbol.for("react.memo"),Ye=Symbol.for("react.lazy"),Ze=Symbol.for("react.scope"),$e=Symbol.for("react.debug_trace_mode"),af=Symbol.for("react.offscreen"),
bf=Symbol.for("react.legacy_hidden"),cf=Symbol.for("react.cache"),df=Symbol.for("react.default_value"),ef=Symbol.for("react.memo_cache_sentinel"),ff=Symbol.for("react.postpone"),gf=Symbol.iterator;function hf(a){if(null===a||"object"!==typeof a)return null;a=gf&&a[gf]||a["@@iterator"];return"function"===typeof a?a:null}
function jf(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Oe:return"Fragment";case Ne:return"Portal";case Qe:return"Profiler";case Pe:return"StrictMode";case Ve:return"Suspense";case We:return"SuspenseList";case cf:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Se:return(a.displayName||"Context")+".Consumer";case Re:return(a._context.displayName||"Context")+".Provider";case Ue:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case Xe:return b=a.displayName||null,null!==b?b:jf(a.type)||"Memo";case Ye:b=a._payload;a=a._init;try{return jf(a(b))}catch(c){break}case Te:return(a.displayName||a._globalName)+".Provider"}return null}var kf={};function lf(a,b){a=a.contextTypes;if(!a)return kf;var c={},d;for(d in a)c[d]=b[d];return c}var mf=null;
function nf(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error(h(401));}else{if(null===c)throw Error(h(401));nf(a,c)}b.context._currentValue=b.value}}function of(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&of(a)}function pf(a){var b=a.parent;null!==b&&pf(b);a.context._currentValue=a.value}
function qf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error(h(402));a.depth===b.depth?nf(a,b):qf(a,b)}function rf(a,b){var c=b.parent;if(null===c)throw Error(h(402));a.depth===c.depth?nf(a,c):rf(a,c);b.context._currentValue=b.value}function sf(a){var b=mf;b!==a&&(null===b?pf(a):null===a?of(b):b.depth===a.depth?nf(b,a):b.depth>a.depth?qf(b,a):rf(b,a),mf=a)}
var tf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function uf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=tf;a.props=c;a.state=e;var g={queue:[],replace:!1};a._reactInternals=g;var f=b.contextType;a.context="object"===typeof f&&null!==f?f._currentValue:d;f=b.getDerivedStateFromProps;"function"===typeof f&&(f=f(c,e),e=null===f||void 0===f?e:ta({},e,f),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&tf.enqueueReplaceState(a,a.state,null),null!==g.queue&&0<g.queue.length)if(b=g.queue,f=g.replace,g.queue=null,g.replace=!1,f&&1===b.length)a.state=b[0];else{g=f?b[0]:a.state;e=!0;for(f=f?1:0;f<b.length;f++){var m=b[f];m="function"===typeof m?m.call(a,g,c,d):m;null!=m&&(e?(e=!1,g=ta({},g,m)):ta(g,m))}a.state=g}else g.queue=null}
var vf={id:1,overflow:""};function wf(a,b,c){var d=a.id;a=a.overflow;var e=32-xf(d)-1;d&=~(1<<e);c+=1;var g=32-xf(b)+e;if(30<g){var f=e-e%5;g=(d&(1<<f)-1).toString(32);d>>=f;e-=f;return{id:1<<32-xf(b)+e|c<<e|d,overflow:g+a}}return{id:1<<g|c<<e|d,overflow:a}}var xf=Math.clz32?Math.clz32:yf,zf=Math.log,Af=Math.LN2;function yf(a){a>>>=0;return 0===a?32:31-(zf(a)/Af|0)|0}var Bf=Error(h(460));function Cf(){}
function Df(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Cf,Cf),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Ef=b;throw Bf;}}var Ef=null;
function Ff(){if(null===Ef)throw Error(h(459));var a=Ef;Ef=null;return a}function Gf(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Hf="function"===typeof Object.is?Object.is:Gf,If=null,Jf=null,Kf=null,X=null,Lf=!1,Mf=!1,Nf=0,Of=0,Pf=null,Qf=null,Rf=0;function Sf(){if(null===If)throw Error(h(321));return If}function Tf(){if(0<Rf)throw Error(h(312));return{memoizedState:null,queue:null,next:null}}
function Uf(){null===X?null===Kf?(Lf=!1,Kf=X=Tf()):(Lf=!0,X=Kf):null===X.next?(Lf=!1,X=X.next=Tf()):(Lf=!0,X=X.next);return X}function Vf(a,b,c,d){for(;Mf;)Mf=!1,Of=Nf=0,Rf+=1,X=null,c=a(b,d);Wf();return c}function Xf(){var a=Pf;Pf=null;return a}function Wf(){Jf=If=null;Mf=!1;Kf=null;Rf=0;X=Qf=null}function Yf(a,b){return"function"===typeof b?b(a):b}
function Zf(a,b,c){If=Sf();X=Uf();if(Lf){var d=X.queue;b=d.dispatch;if(null!==Qf&&(c=Qf.get(d),void 0!==c)){Qf.delete(d);d=X.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);X.memoizedState=d;return[d,b]}return[X.memoizedState,b]}a=a===Yf?"function"===typeof b?b():b:void 0!==c?c(b):b;X.memoizedState=a;a=X.queue={last:null,dispatch:null};a=a.dispatch=$f.bind(null,If,a);return[X.memoizedState,a]}
function ag(a,b){If=Sf();X=Uf();b=void 0===b?null:b;if(null!==X){var c=X.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Hf(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();X.memoizedState=[a,b];return a}function $f(a,b,c){if(25<=Rf)throw Error(h(301));if(a===If)if(Mf=!0,a={action:c,next:null},null===Qf&&(Qf=new Map),c=Qf.get(b),void 0===c)Qf.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}
function bg(){throw Error(h(440));}function cg(){throw Error(h(394));}function dg(){throw Error(h(479));}function eg(a){var b=Of;Of+=1;null===Pf&&(Pf=[]);return Df(Pf,a,b)}function fg(){throw Error(h(393));}function gg(){}
var ig={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return eg(a);if(a.$$typeof===Se||a.$$typeof===Te)return a._currentValue}throw Error(h(438,String(a)));},useContext:function(a){Sf();return a._currentValue},useMemo:ag,useReducer:Zf,useRef:function(a){If=Sf();X=Uf();var b=X.memoizedState;return null===b?(a={current:a},X.memoizedState=a):b},useState:function(a){return Zf(Yf,a)},useInsertionEffect:gg,useLayoutEffect:gg,
useCallback:function(a,b){return ag(function(){return a},b)},useImperativeHandle:gg,useEffect:gg,useDebugValue:gg,useDeferredValue:function(a){Sf();return a},useTransition:function(){Sf();return[!1,cg]},useId:function(){var a=Jf.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-xf(a)-1)).toString(32)+b;var c=hg;if(null===c)throw Error(h(404));b=Nf++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error(h(407));return c()},useCacheRefresh:function(){return fg},
useEffectEvent:function(){return bg},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=ef;return b},useHostTransitionStatus:function(){Sf();return Oa},useOptimistic:function(a){Sf();return[a,dg]}},hg=null,jg={getCacheSignal:function(){throw Error(h(248));},getCacheForType:function(){throw Error(h(248));}},kg=Na.ReactCurrentDispatcher,lg=Na.ReactCurrentCache;function mg(a){console.error(a);return null}function ng(){}
function og(a,b,c,d,e,g,f,m,n,t,v){Pa.current=lb;var p=[],w=new Set;b={destination:null,flushScheduled:!1,responseState:c,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,resources:b,completedRootSegment:null,abortableTasks:w,pingedTasks:p,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],onError:void 0===g?mg:g,onPostpone:void 0===v?ng:v,onAllReady:void 0===f?ng:f,onShellReady:void 0===m?ng:m,onShellError:void 0===
n?ng:n,onFatalError:void 0===t?ng:t};d=pg(b,0,null,d,!1,!1);d.parentFlushed=!0;a=qg(b,null,a,null,d,w,null,kf,null,vf);p.push(a);return b}var V=null;
function qg(a,b,c,d,e,g,f,m,n,t){a.allPendingTasks++;null===d?a.pendingRootTasks++:d.pendingTasks++;var v={node:c,ping:function(){a.pingedTasks.push(v);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,rg(a))},blockedBoundary:d,blockedSegment:e,abortSet:g,keyPath:f,legacyContext:m,context:n,treeContext:t,thenableState:b};g.add(v);return v}
function pg(a,b,c,d,e,g){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],formatContext:d,boundary:c,lastPushedText:e,textEmbedded:g}}function sg(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}
function tg(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,sa(a.destination,b)):(a.status=1,a.fatalError=b)}function ug(a,b,c,d){var e=c.render(),g=d.childContextTypes;if(null!==g&&void 0!==g){var f=b.legacyContext;if("function"!==typeof c.getChildContext)d=f;else{c=c.getChildContext();for(var m in c)if(!(m in g))throw Error(h(108,jf(d)||"Unknown",m));d=ta({},f,c)}b.legacyContext=d;Z(a,b,null,e,0);b.legacyContext=f}else Z(a,b,null,e,0)}
function vg(a,b){if(a&&a.defaultProps){b=ta({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function wg(a,b,c,d,e,g){if("function"===typeof d)if(d.prototype&&d.prototype.isReactComponent){var f=lf(d,b.legacyContext);c=d.contextType;c=new d(e,"object"===typeof c&&null!==c?c._currentValue:f);uf(c,d,e,f);ug(a,b,c,d)}else if(f=lf(d,b.legacyContext),If={},Jf=b,Of=Nf=0,Pf=c,c=d(e,f),c=Vf(d,e,c,f),g=0!==Nf,"object"===typeof c&&null!==c&&"function"===typeof c.render&&void 0===c.$$typeof)uf(c,d,e,f),ug(a,b,c,d);else if(g){e=b.treeContext;b.treeContext=wf(e,1,0);try{Z(a,b,null,c,0)}finally{b.treeContext=
e}}else Z(a,b,null,c,0);else if("string"===typeof d){f=b.blockedSegment;g=rc(f.chunks,d,e,a.resources,a.responseState,f.formatContext,f.lastPushedText);f.lastPushedText=!1;c=f.formatContext;f.formatContext=Gb(c,d,e);xg(a,b,g,0);f.formatContext=c;a:{b=f.chunks;switch(d){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=
c.insertionMode){a.responseState.hasBody=!0;break a}break;case "html":if(0===c.insertionMode)break a}b.push(kc,x(d),lc)}f.lastPushedText=!1}else{switch(d){case bf:case $e:case Pe:case Qe:case Oe:Z(a,b,null,e.children,0);return;case af:"hidden"!==e.mode&&Z(a,b,null,e.children,0);return;case We:Z(a,b,null,e.children,0);return;case Ze:throw Error(h(343));case Ve:a:{d=b.blockedBoundary;c=b.blockedSegment;g=e.fallback;e=e.children;var m=new Set,n={id:null,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,
forceClientRender:!1,completedSegments:[],byteSize:0,fallbackAbortableTasks:m,errorDigest:null,resources:new Set},t=pg(a,c.chunks.length,n,c.formatContext,!1,!1);c.children.push(t);c.lastPushedText=!1;var v=pg(a,0,null,c.formatContext,!1,!1);v.parentFlushed=!0;b.blockedBoundary=n;b.blockedSegment=v;a.resources.boundaryResources=n.resources;try{if(xg(a,b,e,0),v.lastPushedText&&v.textEmbedded&&v.chunks.push(Hb),v.status=1,yg(n,v),0===n.pendingTasks)break a}catch(p){v.status=4,n.forceClientRender=!0,
"object"===typeof p&&null!==p&&p.$$typeof===ff?(a.onPostpone(p.message),f="POSTPONE"):f=sg(a,p),n.errorDigest=f}finally{a.resources.boundaryResources=d?d.resources:null,b.blockedBoundary=d,b.blockedSegment=c}b=qg(a,null,g,d,t,m,b.keyPath,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof d&&null!==d)switch(d.$$typeof){case Ue:d=d.render;If={};Jf=b;Of=Nf=0;Pf=c;f=d(e,g);e=Vf(d,e,f,g);if(0!==Nf){d=b.treeContext;b.treeContext=wf(d,1,0);try{Z(a,b,null,e,0)}finally{b.treeContext=
d}}else Z(a,b,null,e,0);return;case Xe:d=d.type;e=vg(d,e);wg(a,b,c,d,e,g);return;case Re:f=e.children;d=d._context;e=e.value;c=d._currentValue;d._currentValue=e;g=mf;mf=e={parent:g,depth:null===g?0:g.depth+1,context:d,parentValue:c,value:e};b.context=e;Z(a,b,null,f,0);a=mf;if(null===a)throw Error(h(403));e=a.parentValue;a.context._currentValue=e===df?a.context._defaultValue:e;a=mf=a.parent;b.context=a;return;case Se:e=e.children;e=e(d._currentValue);Z(a,b,null,e,0);return;case Ye:f=d._init;d=f(d._payload);
e=vg(d,e);wg(a,b,c,d,e,void 0);return}throw Error(h(130,null==d?d:typeof d,""));}}
function Z(a,b,c,d,e){b.node=d;if("object"===typeof d&&null!==d){switch(d.$$typeof){case Le:var g=d.type,f=d.key,m=d.props;d=d.ref;var n=jf(g),t=b.keyPath;b.keyPath=[b.keyPath,n,null==f?e:f];wg(a,b,c,g,m,d);b.keyPath=t;return;case Ne:throw Error(h(257));case Ye:c=d._init;d=c(d._payload);Z(a,b,null,d,e);return}if(Ma(d)){zg(a,b,d,e);return}if(c=hf(d))if(c=c.call(d)){d=c.next();if(!d.done){g=[];do g.push(d.value),d=c.next();while(!d.done);zg(a,b,g,e)}return}if("function"===typeof d.then)return Z(a,b,
null,eg(d),e);if(d.$$typeof===Se||d.$$typeof===Te)return Z(a,b,null,d._currentValue,e);a=Object.prototype.toString.call(d);throw Error(h(31,"[object Object]"===a?"object with keys {"+Object.keys(d).join(", ")+"}":a));}"string"===typeof d?(e=b.blockedSegment,e.lastPushedText=Ib(b.blockedSegment.chunks,d,a.responseState,e.lastPushedText)):"number"===typeof d&&(e=b.blockedSegment,e.lastPushedText=Ib(b.blockedSegment.chunks,""+d,a.responseState,e.lastPushedText))}
function zg(a,b,c,d){for(var e=b.keyPath,g=c.length,f=0;f<g;f++){var m=b.treeContext;b.treeContext=wf(m,g,f);try{var n=c[f];if(Ma(n)||hf(n))b.keyPath=[b.keyPath,"",d];xg(a,b,n,f)}finally{b.treeContext=m,b.keyPath=e}}}
function xg(a,b,c,d){var e=b.blockedSegment,g=e.children.length,f=e.chunks.length,m=b.blockedSegment.formatContext,n=b.legacyContext,t=b.context,v=b.keyPath;try{return Z(a,b,null,c,d)}catch(p){if(Wf(),e.children.length=g,e.chunks.length=f,c=p===Bf?Ff():p,"object"===typeof c&&null!==c&&"function"===typeof c.then)d=Xf(),e=b.blockedSegment,g=pg(a,e.chunks.length,null,e.formatContext,e.lastPushedText,!0),e.children.push(g),e.lastPushedText=!1,a=qg(a,d,b.node,b.blockedBoundary,g,b.abortSet,b.keyPath,b.legacyContext,
b.context,b.treeContext).ping,c.then(a,a),b.blockedSegment.formatContext=m,b.legacyContext=n,b.context=t,b.keyPath=v,sf(t);else throw b.blockedSegment.formatContext=m,b.legacyContext=n,b.context=t,b.keyPath=v,sf(t),c;}}function Ag(a){var b=a.blockedBoundary;a=a.blockedSegment;a.status=3;Bg(this,b,a)}
function Cg(a,b,c){var d=a.blockedBoundary;a.blockedSegment.status=3;null===d?(b.allPendingTasks--,1!==b.status&&2!==b.status&&(sg(b,c),tg(b,c))):(d.pendingTasks--,d.forceClientRender||(d.forceClientRender=!0,d.errorDigest=b.onError(c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(e){return Cg(e,b,c)}),d.fallbackAbortableTasks.clear(),b.allPendingTasks--,0===b.allPendingTasks&&(a=b.onAllReady,a()))}
function yg(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&yg(a,c)}else a.completedSegments.push(b)}
function Bg(a,b,c){if(null===b){if(c.parentFlushed){if(null!==a.completedRootSegment)throw Error(h(389));a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&(a.onShellError=ng,b=a.onShellReady,b())}else b.pendingTasks--,b.forceClientRender||(0===b.pendingTasks?(c.parentFlushed&&1===c.status&&yg(b,c),b.parentFlushed&&a.completedBoundaries.push(b),b.fallbackAbortableTasks.forEach(Ag,a),b.fallbackAbortableTasks.clear()):c.parentFlushed&&1===c.status&&(yg(b,c),1===b.completedSegments.length&&
b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&(a=a.onAllReady,a())}
function rg(a){if(2!==a.status){var b=mf,c=kg.current;kg.current=ig;var d=lg.current;lg.current=jg;var e=V;V=a;var g=hg;hg=a.responseState;try{var f=a.pingedTasks,m;for(m=0;m<f.length;m++){var n=f[m];var t=a,v=n.blockedBoundary;t.resources.boundaryResources=v?v.resources:null;var p=n.blockedSegment;if(0===p.status){sf(n.context);var w=p.children.length,H=p.chunks.length;try{var I=n.thenableState;n.thenableState=null;Z(t,n,I,n.node,0);p.lastPushedText&&p.textEmbedded&&p.chunks.push(Hb);n.abortSet.delete(n);
p.status=1;Bg(t,n.blockedBoundary,p)}catch(C){Wf();p.children.length=w;p.chunks.length=H;var E=C===Bf?Ff():C;if("object"===typeof E&&null!==E&&"function"===typeof E.then){var u=n.ping;E.then(u,u);n.thenableState=Xf()}else{n.abortSet.delete(n);p.status=4;var P=void 0,Q=t,L=n.blockedBoundary,B=E;"object"===typeof B&&null!==B&&B.$$typeof===ff?(Q.onPostpone(B.message),P="POSTPONE"):P=sg(Q,B);null===L?tg(Q,B):(L.pendingTasks--,L.forceClientRender||(L.forceClientRender=!0,L.errorDigest=P,L.parentFlushed&&
Q.clientRenderedBoundaries.push(L)));Q.allPendingTasks--;if(0===Q.allPendingTasks){var R=Q.onAllReady;R()}}}finally{t.resources.boundaryResources=null}}}f.splice(0,m);null!==a.destination&&Dg(a,a.destination)}catch(C){sg(a,C),tg(a,C)}finally{hg=g,kg.current=c,lg.current=d,c===ig&&sf(b),V=e}}}
function Eg(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:var d=c.id=a.nextSegmentId++;c.lastPushedText=!1;c.textEmbedded=!1;a=a.responseState;q(b,tc);q(b,a.placeholderPrefix);a=x(d.toString(16));q(b,a);return r(b,uc);case 1:c.status=2;var e=!0;d=c.chunks;var g=0;c=c.children;for(var f=0;f<c.length;f++){for(e=c[f];g<e.index;g++)q(b,d[g]);e=Fg(a,b,e)}for(;g<d.length-1;g++)q(b,d[g]);g<d.length&&(e=r(b,d[g]));return e;default:throw Error(h(390));}}
function Fg(a,b,c){var d=c.boundary;if(null===d)return Eg(a,b,c);d.parentFlushed=!0;if(d.forceClientRender)d=d.errorDigest,r(b,yc),q(b,Ac),d&&(q(b,Cc),q(b,x(A(d))),q(b,Bc)),r(b,Dc),Eg(a,b,c);else if(0<d.pendingTasks){d.rootSegmentID=a.nextSegmentId++;0<d.completedSegments.length&&a.partialBoundaries.push(d);var e=a.responseState;var g=e.nextSuspenseID++;e=y(e.boundaryPrefix+g.toString(16));d=d.id=e;Ec(b,a.responseState,d);Eg(a,b,c)}else if(d.byteSize>a.progressiveChunkSize)d.rootSegmentID=a.nextSegmentId++,
a.completedBoundaries.push(d),Ec(b,a.responseState,d.id),Eg(a,b,c);else{(c=a.resources.boundaryResources)&&d.resources.forEach(Ke,c);r(b,vc);c=d.completedSegments;if(1!==c.length)throw Error(h(391));Fg(a,b,c[0])}return r(b,zc)}function Gg(a,b,c){Bd(b,a.responseState,c.formatContext,c.id);Fg(a,b,c);return Cd(b,c.formatContext)}
function Hg(a,b,c){a.resources.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)Ig(a,b,c,d[e]);d.length=0;qe(b,c.resources,a.responseState);a=a.responseState;d=c.id;e=c.rootSegmentID;c=c.resources;var g=a.stylesToHoist;a.stylesToHoist=!1;var f=0===a.streamingFormat;f?(q(b,a.startInlineScript),g?0===(a.instructions&2)?(a.instructions|=10,q(b,512<Ld.byteLength?Ld.slice():Ld)):0===(a.instructions&8)?(a.instructions|=8,q(b,Md)):q(b,Nd):0===(a.instructions&2)?(a.instructions|=
2,q(b,Jd)):q(b,Kd)):g?q(b,Td):q(b,Sd);if(null===d)throw Error(h(395));e=x(e.toString(16));q(b,d);f?q(b,Od):q(b,Ud);q(b,a.segmentPrefix);q(b,e);g?f?(q(b,Pd),He(b,c)):(q(b,Vd),Ie(b,c)):f&&q(b,Qd);d=f?r(b,Rd):r(b,mb);return sc(b,a)&&d}
function Ig(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error(h(392));return Gg(a,b,d)}Gg(a,b,d);a=a.responseState;(c=0===a.streamingFormat)?(q(b,a.startInlineScript),0===(a.instructions&1)?(a.instructions|=1,q(b,Dd)):q(b,Ed)):q(b,Hd);q(b,a.segmentPrefix);e=x(e.toString(16));q(b,e);c?q(b,Fd):q(b,Id);q(b,a.placeholderPrefix);q(b,e);b=c?r(b,Gd):r(b,mb);return b}
function Dg(a,b){k=new Uint8Array(512);l=0;try{var c,d=a.completedRootSegment;if(null!==d)if(0===a.pendingRootTasks){var e=a.resources,g=a.responseState;if(0!==a.allPendingTasks&&g.externalRuntimeScript){var f=g.externalRuntimeScript,m=f.chunks,n="[script]"+f.src,t=e.scriptsMap.get(n);t||(t={type:"script",chunks:m,state:0,props:null},e.scriptsMap.set(n,t),e.scripts.add(t))}var v=g.htmlChunks,p=g.headChunks;f=0;if(v){for(f=0;f<v.length;f++)q(b,v[f]);if(p)for(f=0;f<p.length;f++)q(b,p[f]);else q(b,U("head")),
q(b,T)}else if(p)for(f=0;f<p.length;f++)q(b,p[f]);var w=g.charsetChunks;for(f=0;f<w.length;f++)q(b,w[f]);w.length=0;e.preconnects.forEach(re,b);e.preconnects.clear();var H=g.preconnectChunks;for(f=0;f<H.length;f++)q(b,H[f]);H.length=0;e.fontPreloads.forEach(re,b);e.fontPreloads.clear();e.highImagePreloads.forEach(re,b);e.highImagePreloads.clear();e.precedences.forEach(Ae,b);e.bootstrapScripts.forEach(re,b);e.scripts.forEach(re,b);e.scripts.clear();e.bulkPreloads.forEach(re,b);e.bulkPreloads.clear();
var I=g.preloadChunks;for(f=0;f<I.length;f++)q(b,I[f]);I.length=0;var E=g.hoistableChunks;for(f=0;f<E.length;f++)q(b,E[f]);E.length=0;v&&null===p&&(q(b,kc),q(b,x("head")),q(b,lc));Fg(a,b,d);a.completedRootSegment=null;sc(b,a.responseState)}else return;else if(0<a.pendingRootTasks)return;var u=a.resources,P=a.responseState;d=0;u.preconnects.forEach(se,b);u.preconnects.clear();var Q=P.preconnectChunks;for(d=0;d<Q.length;d++)q(b,Q[d]);Q.length=0;u.fontPreloads.forEach(se,b);u.fontPreloads.clear();u.highImagePreloads.forEach(re,
b);u.highImagePreloads.clear();u.precedences.forEach(Ce,b);u.scripts.forEach(se,b);u.scripts.clear();u.bulkPreloads.forEach(se,b);u.bulkPreloads.clear();var L=P.preloadChunks;for(d=0;d<L.length;d++)q(b,L[d]);L.length=0;var B=P.hoistableChunks;for(d=0;d<B.length;d++)q(b,B[d]);B.length=0;var R=a.clientRenderedBoundaries;for(c=0;c<R.length;c++){var C=R[c];u=b;var D=a.responseState,ja=C.id,Ga=C.errorDigest,wa=C.errorMessage,ka=C.errorComponentStack,ea=0===D.streamingFormat;ea?(q(u,D.startInlineScript),
0===(D.instructions&4)?(D.instructions|=4,q(u,Wd)):q(u,Xd)):q(u,ae);if(null===ja)throw Error(h(395));q(u,ja);ea&&q(u,Yd);if(Ga||wa||ka)ea?(q(u,Zd),q(u,x(fe(Ga||"")))):(q(u,be),q(u,x(A(Ga||""))));if(wa||ka)ea?(q(u,Zd),q(u,x(fe(wa||"")))):(q(u,ce),q(u,x(A(wa||""))));ka&&(ea?(q(u,Zd),q(u,x(fe(ka)))):(q(u,de),q(u,x(A(ka)))));if(ea?!r(u,$d):!r(u,mb)){a.destination=null;c++;R.splice(0,c);return}}R.splice(0,c);var xa=a.completedBoundaries;for(c=0;c<xa.length;c++)if(!Hg(a,b,xa[c])){a.destination=null;c++;
xa.splice(0,c);return}xa.splice(0,c);ia(b);k=new Uint8Array(512);l=0;var la=a.partialBoundaries;for(c=0;c<la.length;c++){var ma=la[c];a:{R=a;C=b;R.resources.boundaryResources=ma.resources;var na=ma.completedSegments;for(D=0;D<na.length;D++)if(!Ig(R,C,ma,na[D])){D++;na.splice(0,D);var Ha=!1;break a}na.splice(0,D);Ha=qe(C,ma.resources,R.responseState)}if(!Ha){a.destination=null;c++;la.splice(0,c);return}}la.splice(0,c);var ca=a.completedBoundaries;for(c=0;c<ca.length;c++)if(!Hg(a,b,ca[c])){a.destination=
null;c++;ca.splice(0,c);return}ca.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?(a.flushScheduled=!1,a=a.responseState,a.hasBody&&(q(b,kc),q(b,x("body")),q(b,lc)),a.htmlChunks&&(q(b,kc),q(b,x("html")),q(b,lc)),ia(b),b.close()):ia(b)}}function Je(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;Dg(a,b)}}
function Jg(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error(h(432)):b;c.forEach(function(e){return Cg(e,a,d)});c.clear()}null!==a.destination&&Dg(a,a.destination)}catch(e){sg(a,e),tg(a,e)}}
exports.prerender=function(a,b){return new Promise(function(c,d){var e={preloadsMap:new Map,preconnectsMap:new Map,stylesMap:new Map,scriptsMap:new Map,preconnects:new Set,fontPreloads:new Set,highImagePreloads:new Set,precedences:new Map,stylePrecedences:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,boundaryResources:null},g=og(a,e,Db(e,b?b.identifierPrefix:void 0,void 0,b?b.bootstrapScriptContent:void 0,b?b.bootstrapScripts:void 0,b?b.bootstrapModules:void 0,b?b.unstable_externalRuntimeSrc:
void 0),Fb(b?b.namespaceURI:void 0),b?b.progressiveChunkSize:void 0,b?b.onError:void 0,function(){var n={prelude:new ReadableStream({type:"bytes",pull:function(t){if(1===g.status)g.status=2,sa(t,g.fatalError);else if(2!==g.status&&null===g.destination){g.destination=t;try{Dg(g,t)}catch(v){sg(g,v),tg(g,v)}}}},{highWaterMark:0})};c(n)},void 0,void 0,d,b?b.onPostpone:void 0);if(b&&b.signal){var f=b.signal;if(f.aborted)Jg(g,f.reason);else{var m=function(){Jg(g,f.reason);f.removeEventListener("abort",
m)};f.addEventListener("abort",m)}}g.flushScheduled=null!==g.destination;rg(g)})};exports.version="18.3.0-experimental-dd480ef92-20230822";
