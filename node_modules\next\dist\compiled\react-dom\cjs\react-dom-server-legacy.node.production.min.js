/**
 * @license React
 * react-dom-server-legacy.node.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/*


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var da=require("next/dist/compiled/react"),ha=require("react-dom"),ia=require("stream");
function ja(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}
var r=Object.assign,u=Object.prototype.hasOwnProperty,ka=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),la={},ya={};
function za(a){if(u.call(ya,a))return!0;if(u.call(la,a))return!1;if(ka.test(a))return ya[a]=!0;la[a]=!0;return!1}
var Aa=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Ba=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ca=/["'&<>]/;
function v(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ca.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ha=/([A-Z])/g,Ia=/^ms-/,Ja=Array.isArray,Ka=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,La={pending:!1,data:null,method:null,action:null},Ma=ha.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,kb={prefetchDNS:db,preconnect:eb,preload:fb,preloadModule:gb,preinitStyle:hb,preinitScript:ib,preinitModuleScript:jb},lb=[];
function mb(a,b){var c=0;void 0!==b&&(c=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:c,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function x(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function nb(a){return x("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function ob(a,b,c){switch(b){case "noscript":return x(2,null,a.tagScope|1);case "select":return x(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return x(3,null,a.tagScope);case "picture":return x(2,null,a.tagScope|2);case "math":return x(4,null,a.tagScope);case "foreignObject":return x(2,null,a.tagScope);case "table":return x(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return x(6,null,a.tagScope);case "colgroup":return x(8,null,a.tagScope);case "tr":return x(7,null,a.tagScope)}return 5<=
a.insertionMode?x(2,null,a.tagScope):0===a.insertionMode?"html"===b?x(1,null,a.tagScope):x(2,null,a.tagScope):1===a.insertionMode?x(2,null,a.tagScope):a}var yb=new Map;
function zb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(u.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=v(d);e=v((""+e).trim())}else f=yb.get(d),void 0===f&&(f=v(d.replace(Ha,"-$1").toLowerCase().replace(Ia,"-ms-")),yb.set(d,f)),e="number"===typeof e?0===e||Aa.has(d)?""+e:e+"px":
v((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}function Ab(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function F(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',v(c),'"')}function Bb(a){var b=a.nextFormID++;return a.idPrefix+b}var Cb=v("javascript:throw new Error('A React form was unexpectedly submitted.')");
function Db(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");F(this,"name",b);F(this,"value",a);this.push("/>")}
function Eb(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Bb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',Cb,'"'),g=f=e=d=h=null,Fb(b,c)));null!=h&&G(a,"name",h);null!=d&&G(a,"formAction",d);null!=e&&G(a,"formEncType",e);null!=f&&G(a,"formMethod",f);null!=g&&G(a,"formTarget",g);return k}
function G(a,b,c){switch(b){case "className":F(a,"class",c);break;case "tabIndex":F(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":F(a,b,c);break;case "style":zb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',v(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Ab(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',v(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',v(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',v(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',v(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',v(c),'"');break;case "xlinkActuate":F(a,"xlink:actuate",
c);break;case "xlinkArcrole":F(a,"xlink:arcrole",c);break;case "xlinkRole":F(a,"xlink:role",c);break;case "xlinkShow":F(a,"xlink:show",c);break;case "xlinkTitle":F(a,"xlink:title",c);break;case "xlinkType":F(a,"xlink:type",c);break;case "xmlBase":F(a,"xml:base",c);break;case "xmlLang":F(a,"xml:lang",c);break;case "xmlSpace":F(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Ba.get(b)||b,za(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',v(c),'"')}}}function H(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function Gb(a){var b="";da.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function Fb(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});',"\x3c/script>"))}
function Hb(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return I(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return I(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:v(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:r({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&Ib(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return I(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return I(d.preconnectChunks,b);case "preload":return I(d.preloadChunks,
b);default:return I(d.hoistableChunks,b)}}function I(a,b){a.push(J("link"));for(var c in b)if(u.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:G(a,c,d)}}a.push("/>");return null}
function Jb(a,b,c){a.push(J(c));for(var d in b)if(u.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:G(a,d,e)}}a.push("/>");return null}
function Kb(a,b){a.push(J("title"));var c=null,d=null,e;for(e in b)if(u.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:G(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(v(""+b));H(a,d,c);a.push(Lb("title"));return null}
function Mb(a,b){a.push(J("script"));var c=null,d=null,e;for(e in b)if(u.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:G(a,e,f)}}a.push(">");H(a,d,c);"string"===typeof c&&a.push(v(c));a.push(Lb("script"));return null}
function Nb(a,b,c){a.push(J(c));var d=c=null,e;for(e in b)if(u.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:G(a,e,f)}}a.push(">");H(a,d,c);return"string"===typeof c?(a.push(v(c)),null):c}var Ob=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Pb=new Map;function J(a){var b=Pb.get(a);if(void 0===b){if(!Ob.test(a))throw Error("Invalid tag: "+a);b="<"+a;Pb.set(a,b)}return b}
function Qb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(J("select"));var h=null,k=null,l;for(l in c)if(u.call(c,l)){var n=c[l];if(null!=n)switch(l){case "children":h=n;break;case "dangerouslySetInnerHTML":k=n;break;case "defaultValue":case "value":break;default:G(a,l,n)}}a.push(">");H(a,k,h);return h;case "option":var q=f.selectedValue;a.push(J("option"));var m=null,B=null,y=null,P=null,t;for(t in c)if(u.call(c,
t)){var w=c[t];if(null!=w)switch(t){case "children":m=w;break;case "selected":y=w;break;case "dangerouslySetInnerHTML":P=w;break;case "value":B=w;default:G(a,t,w)}}if(null!=q){var p=null!==B?""+B:Gb(m);if(Ja(q))for(var S=0;S<q.length;S++){if(""+q[S]===p){a.push(' selected=""');break}}else""+q===p&&a.push(' selected=""')}else y&&a.push(' selected=""');a.push(">");H(a,P,m);return m;case "textarea":a.push(J("textarea"));var C=null,T=null,D=null,L;for(L in c)if(u.call(c,L)){var z=c[L];if(null!=z)switch(L){case "children":D=
z;break;case "value":C=z;break;case "defaultValue":T=z;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:G(a,L,z)}}null===C&&null!==T&&(C=T);a.push(">");if(null!=D){if(null!=C)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ja(D)){if(1<D.length)throw Error("<textarea> can only have at most one child.");C=""+D[0]}C=""+D}"string"===typeof C&&"\n"===C[0]&&a.push("\n");null!==C&&a.push(v(""+C));
return null;case "input":a.push(J("input"));var ma=null,U=null,aa=null,M=null,X=null,A=null,Na=null,Oa=null,Pa=null,na;for(na in c)if(u.call(c,na)){var Q=c[na];if(null!=Q)switch(na){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":ma=Q;break;case "formAction":U=Q;break;case "formEncType":aa=Q;break;case "formMethod":M=Q;break;case "formTarget":X=Q;break;case "defaultChecked":Pa=
Q;break;case "defaultValue":Na=Q;break;case "checked":Oa=Q;break;case "value":A=Q;break;default:G(a,na,Q)}}var pb=Eb(a,d,e,U,aa,M,X,ma);null!==Oa?Ab(a,"checked",Oa):null!==Pa&&Ab(a,"checked",Pa);null!==A?G(a,"value",A):null!==Na&&G(a,"value",Na);a.push("/>");null!==pb&&pb.forEach(Db,a);return null;case "button":a.push(J("button"));var oa=null,pa=null,ba=null,qa=null,ra=null,Qa=null,sa=null,Ra;for(Ra in c)if(u.call(c,Ra)){var ca=c[Ra];if(null!=ca)switch(Ra){case "children":oa=ca;break;case "dangerouslySetInnerHTML":pa=
ca;break;case "name":ba=ca;break;case "formAction":qa=ca;break;case "formEncType":ra=ca;break;case "formMethod":Qa=ca;break;case "formTarget":sa=ca;break;default:G(a,Ra,ca)}}var Ec=Eb(a,d,e,qa,ra,Qa,sa,ba);a.push(">");null!==Ec&&Ec.forEach(Db,a);H(a,pa,oa);if("string"===typeof oa){a.push(v(oa));var Fc=null}else Fc=oa;return Fc;case "form":a.push(J("form"));var Sa=null,Gc=null,ea=null,Ta=null,Ua=null,Va=null,Wa;for(Wa in c)if(u.call(c,Wa)){var fa=c[Wa];if(null!=fa)switch(Wa){case "children":Sa=fa;
break;case "dangerouslySetInnerHTML":Gc=fa;break;case "action":ea=fa;break;case "encType":Ta=fa;break;case "method":Ua=fa;break;case "target":Va=fa;break;default:G(a,Wa,fa)}}var Tb=null,Ub=null;if("function"===typeof ea)if("function"===typeof ea.$$FORM_ACTION){var ke=Bb(d),Da=ea.$$FORM_ACTION(ke);ea=Da.action||"";Ta=Da.encType;Ua=Da.method;Va=Da.target;Tb=Da.data;Ub=Da.name}else a.push(" ","action",'="',Cb,'"'),Va=Ua=Ta=ea=null,Fb(d,e);null!=ea&&G(a,"action",ea);null!=Ta&&G(a,"encType",Ta);null!=
Ua&&G(a,"method",Ua);null!=Va&&G(a,"target",Va);a.push(">");null!==Ub&&(a.push('<input type="hidden"'),F(a,"name",Ub),a.push("/>"),null!==Tb&&Tb.forEach(Db,a));H(a,Gc,Sa);if("string"===typeof Sa){a.push(v(Sa));var Hc=null}else Hc=Sa;return Hc;case "menuitem":a.push(J("menuitem"));for(var qb in c)if(u.call(c,qb)){var Ic=c[qb];if(null!=Ic)switch(qb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:G(a,qb,Ic)}}a.push(">");
return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Jc=Kb(a,c);else Kb(e.hoistableChunks,c),Jc=null;return Jc;case "link":return Hb(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var Vb=c.async;if("string"!==typeof c.src||!c.src||!Vb||"function"===typeof Vb||"symbol"===typeof Vb||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Kc=Mb(a,c);else{var rb=c.src;if("module"===c.type){var sb=d.moduleScriptResources;var Lc=e.preloads.moduleScripts}else sb=
d.scriptResources,Lc=e.preloads.scripts;var tb=sb.hasOwnProperty(rb)?sb[rb]:void 0;if(null!==tb){sb[rb]=null;var Wb=c;if(tb){2===tb.length&&(Wb=r({},c),Ib(Wb,tb));var Mc=Lc.get(rb);Mc&&(Mc.length=0)}var Nc=[];e.scripts.add(Nc);Mb(Nc,Wb)}g&&a.push("\x3c!-- --\x3e");Kc=null}return Kc;case "style":var ub=c.precedence,ta=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof ub||"string"!==typeof ta||""===ta){a.push(J("style"));var Ea=null,Oc=null,Xa;for(Xa in c)if(u.call(c,
Xa)){var vb=c[Xa];if(null!=vb)switch(Xa){case "children":Ea=vb;break;case "dangerouslySetInnerHTML":Oc=vb;break;default:G(a,Xa,vb)}}a.push(">");var Ya=Array.isArray(Ea)?2>Ea.length?Ea[0]:null:Ea;"function"!==typeof Ya&&"symbol"!==typeof Ya&&null!==Ya&&void 0!==Ya&&a.push(v(""+Ya));H(a,Oc,Ea);a.push(Lb("style"));var Pc=null}else{var ua=e.styles.get(ub);if(null!==(d.styleResources.hasOwnProperty(ta)?d.styleResources[ta]:void 0)){d.styleResources[ta]=null;ua?ua.hrefs.push(v(ta)):(ua={precedence:v(ub),
rules:[],hrefs:[v(ta)],sheets:new Map},e.styles.set(ub,ua));var Qc=ua.rules,Fa=null,Rc=null,wb;for(wb in c)if(u.call(c,wb)){var Xb=c[wb];if(null!=Xb)switch(wb){case "children":Fa=Xb;break;case "dangerouslySetInnerHTML":Rc=Xb}}var Za=Array.isArray(Fa)?2>Fa.length?Fa[0]:null:Fa;"function"!==typeof Za&&"symbol"!==typeof Za&&null!==Za&&void 0!==Za&&Qc.push(v(""+Za));H(Qc,Rc,Fa)}ua&&e.boundaryResources&&e.boundaryResources.styles.add(ua);g&&a.push("\x3c!-- --\x3e");Pc=void 0}return Pc;case "meta":if(3===
f.insertionMode||f.tagScope&1||null!=c.itemProp)var Sc=Jb(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),Sc="string"===typeof c.charSet?Jb(e.charsetChunks,c,"meta"):"viewport"===c.name?Jb(e.preconnectChunks,c,"meta"):Jb(e.hoistableChunks,c,"meta");return Sc;case "listing":case "pre":a.push(J(b));var $a=null,ab=null,bb;for(bb in c)if(u.call(c,bb)){var xb=c[bb];if(null!=xb)switch(bb){case "children":$a=xb;break;case "dangerouslySetInnerHTML":ab=xb;break;default:G(a,bb,xb)}}a.push(">");if(null!=ab){if(null!=
$a)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof ab||!("__html"in ab))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var va=ab.__html;null!==va&&void 0!==va&&("string"===typeof va&&0<va.length&&"\n"===va[0]?a.push("\n",va):a.push(""+va))}"string"===typeof $a&&"\n"===$a[0]&&a.push("\n");return $a;case "img":var N=c.src,
E=c.srcSet;if(!("lazy"===c.loading||!N&&!E||"string"!==typeof N&&null!=N||"string"!==typeof E&&null!=E)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof N||":"!==N[4]||"d"!==N[0]&&"D"!==N[0]||"a"!==N[1]&&"A"!==N[1]||"t"!==N[2]&&"T"!==N[2]||"a"!==N[3]&&"A"!==N[3])&&("string"!==typeof E||":"!==E[4]||"d"!==E[0]&&"D"!==E[0]||"a"!==E[1]&&"A"!==E[1]||"t"!==E[2]&&"T"!==E[2]||"a"!==E[3]&&"A"!==E[3])){var Tc="string"===typeof c.sizes?c.sizes:void 0,cb=E?E+"\n"+(Tc||""):N,Yb=e.preloads.images,
wa=Yb.get(cb);if(wa){if("high"===c.fetchPriority||10>e.highImagePreloads.size)Yb.delete(cb),e.highImagePreloads.add(wa)}else d.imageResources.hasOwnProperty(cb)||(d.imageResources[cb]=lb,wa=[],I(wa,{rel:"preload",as:"image",href:E?void 0:N,imageSrcSet:E,imageSizes:Tc,crossOrigin:c.crossOrigin,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(wa):(e.bulkPreloads.add(wa),Yb.set(cb,
wa)))}return Jb(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Jb(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Uc=Nb(e.headChunks,c,"head")}else Uc=Nb(a,c,"head");return Uc;case "html":if(0===
f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[""];var Vc=Nb(e.htmlChunks,c,"html")}else Vc=Nb(a,c,"html");return Vc;default:if(-1!==b.indexOf("-")){a.push(J(b));var Zb=null,Wc=null,Ga;for(Ga in c)if(u.call(c,Ga)){var xa=c[Ga];if(null!=xa){var le=Ga;switch(Ga){case "children":Zb=xa;break;case "dangerouslySetInnerHTML":Wc=xa;break;case "style":zb(a,xa);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:za(Ga)&&"function"!==typeof xa&&"symbol"!==typeof xa&&
a.push(" ",le,'="',v(xa),'"')}}}a.push(">");H(a,Wc,Zb);return Zb}}return Nb(a,c,b)}var Rb=new Map;function Lb(a){var b=Rb.get(a);void 0===b&&(b="</"+a+">",Rb.set(a,b));return b}function Sb(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}
function $b(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function ac(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error("Unknown insertion mode. This is a bug in React.");}}
function bc(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error("Unknown insertion mode. This is a bug in React.");}}var cc=/[<\u2028\u2029]/g;
function dc(a){return JSON.stringify(a).replace(cc,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var ec=/[&><\u2028\u2029]/g;
function fc(a){return JSON.stringify(a).replace(ec,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var gc=!1,hc=!0;
function ic(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);hc=this.push("</style>");gc=!0;b.length=0;c.length=0}}function jc(a){return 2!==a.state?gc=!0:!1}function kc(a,b,c){gc=!1;hc=!0;b.styles.forEach(ic,a);b.stylesheets.forEach(jc);gc&&(c.stylesToHoist=!0);return hc}
function K(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var lc=[];function mc(a){I(lc,a.props);for(var b=0;b<lc.length;b++)this.push(lc[b]);lc.length=0;a.state=2}
function nc(a){var b=0<a.sheets.size;a.sheets.forEach(mc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function oc(a){if(0===a.state){a.state=1;var b=a.props;I(lc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<lc.length;a++)this.push(lc[a]);lc.length=0}}function pc(a){a.sheets.forEach(oc,this);a.sheets.clear()}
function qc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=fc(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=fc(""+d.props.href);a.push(g);e=""+e;a.push(",");e=fc(e);a.push(e);for(var h in f)if(u.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!za(h))break a;g=""+g}e.push(",");k=fc(k);e.push(k);e.push(",");g=
fc(g);e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function rc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=v(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=v(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=v(JSON.stringify(e));a.push(e);for(var h in f)if(u.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!za(h))break a;g=""+g}e.push(",");k=v(JSON.stringify(k));e.push(k);
e.push(",");g=v(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}function db(a){var b=O?O:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){var e=[];c.dnsResources[a]=null;I(e,{href:a,rel:"dns-prefetch"});d.preconnects.add(e)}sc(b)}}}
function eb(a,b){var c=O?O:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){d="use-credentials"===b?d.connectResources.credentials:"string"===typeof b?d.connectResources.anonymous:d.connectResources.default;if(!d.hasOwnProperty(a)){var f=[];d[a]=null;I(f,{rel:"preconnect",href:a,crossOrigin:b});e.preconnects.add(f)}sc(c)}}}
function fb(a,b,c){var d=O?O:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}h=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(h))return;e.imageResources[h]=lb;e=[];I(e,r({rel:"preload",href:g?void 0:a,as:b},c));"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(h,e));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];I(g,r({rel:"preload",href:a,
as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?lb:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);I(g,r({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?lb:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=
e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;e=[];c=r({rel:"preload",href:a,as:b},c);switch(b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}I(e,c);g[a]=lb}sc(d)}}}
function gb(a,b){var c=O?O:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?lb:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=lb}I(f,r({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);sc(c)}}}
function hb(a,b,c){var d=O?O:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:v(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:r({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Ib(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),sc(d))}}}
function ib(a,b){var c=O?O:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=r({src:a,async:!0},b),f&&(2===f.length&&Ib(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Mb(a,b),sc(c))}}}
function jb(a,b){var c=O?O:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=r({src:a,type:"module",async:!0},b),f&&(2===f.length&&Ib(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Mb(a,b),sc(c))}}}function Ib(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}function tc(a){this.styles.add(a)}
function uc(a){this.stylesheets.add(a)}
function vc(a,b){var c=a.idPrefix;a=c+"P:";var d=c+"S:";c+="B:";var e="<script>",f=null,g=null,h=null,k=[],l=[],n=[],q=[],m=[],B=[],y=new Set,P=new Set,t=new Set,w=new Map,p=new Set,S=new Set,C=new Set,T={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};return{placeholderPrefix:a,segmentPrefix:d,boundaryPrefix:c,startInlineScript:e,htmlChunks:f,headChunks:g,externalRuntimeScript:h,bootstrapChunks:k,charsetChunks:l,preconnectChunks:n,importMapChunks:q,preloadChunks:m,hoistableChunks:B,
preconnects:y,fontPreloads:P,highImagePreloads:t,styles:w,bootstrapScripts:p,scripts:S,bulkPreloads:C,preloads:T,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function wc(a,b,c,d){if(c.generateStaticMarkup)return a.push(v(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(v(b)),a=!0);return a}
var xc=Symbol.for("react.element"),yc=Symbol.for("react.portal"),zc=Symbol.for("react.fragment"),Ac=Symbol.for("react.strict_mode"),Bc=Symbol.for("react.profiler"),Cc=Symbol.for("react.provider"),Dc=Symbol.for("react.context"),Xc=Symbol.for("react.server_context"),Yc=Symbol.for("react.forward_ref"),Zc=Symbol.for("react.suspense"),$c=Symbol.for("react.suspense_list"),ad=Symbol.for("react.memo"),bd=Symbol.for("react.lazy"),cd=Symbol.for("react.scope"),dd=Symbol.for("react.debug_trace_mode"),ed=Symbol.for("react.offscreen"),
fd=Symbol.for("react.legacy_hidden"),gd=Symbol.for("react.cache"),hd=Symbol.for("react.default_value"),id=Symbol.iterator;
function jd(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case zc:return"Fragment";case yc:return"Portal";case Bc:return"Profiler";case Ac:return"StrictMode";case Zc:return"Suspense";case $c:return"SuspenseList";case gd:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Dc:return(a.displayName||"Context")+".Consumer";case Cc:return(a._context.displayName||"Context")+".Provider";case Yc:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case ad:return b=a.displayName||null,null!==b?b:jd(a.type)||"Memo";case bd:b=a._payload;a=a._init;try{return jd(a(b))}catch(c){}}return null}var kd={};function ld(a,b){a=a.contextTypes;if(!a)return kd;var c={},d;for(d in a)c[d]=b[d];return c}var md=null;
function nd(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");nd(a,c)}b.context._currentValue2=b.value}}function od(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&od(a)}
function pd(a){var b=a.parent;null!==b&&pd(b);a.context._currentValue2=a.value}function qd(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?nd(a,b):qd(a,b)}
function rd(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?nd(a,c):rd(a,c);b.context._currentValue2=b.value}function sd(a){var b=md;b!==a&&(null===b?pd(a):null===a?od(b):b.depth===a.depth?nd(b,a):b.depth>a.depth?qd(b,a):rd(b,a),md=a)}
var td={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function ud(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=td;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:r({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&td.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=r({},f,h)):r(f,h))}a.state=f}else f.queue=null}
var vd={id:1,overflow:""};function wd(a,b,c){var d=a.id;a=a.overflow;var e=32-xd(d)-1;d&=~(1<<e);c+=1;var f=32-xd(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-xd(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var xd=Math.clz32?Math.clz32:yd,zd=Math.log,Ad=Math.LN2;function yd(a){a>>>=0;return 0===a?32:31-(zd(a)/Ad|0)|0}var Bd=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Cd(){}function Dd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Cd,Cd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Ed=b;throw Bd;}}var Ed=null;
function Fd(){if(null===Ed)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Ed;Ed=null;return a}function Gd(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Hd="function"===typeof Object.is?Object.is:Gd,R=null,Id=null,Jd=null,Kd=null,Ld=null,V=null,Md=!1,Nd=!1,Od=0,Pd=0,Qd=-1,Rd=0,Sd=null,Td=null,Ud=0;
function Vd(){if(null===R)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return R}
function Wd(){if(0<Ud)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function Xd(){null===V?null===Ld?(Md=!1,Ld=V=Wd()):(Md=!0,V=Ld):null===V.next?(Md=!1,V=V.next=Wd()):(Md=!0,V=V.next);return V}function Yd(a,b,c,d){for(;Nd;)Nd=!1,Pd=Od=0,Qd=-1,Rd=0,Ud+=1,V=null,c=a(b,d);Zd();return c}function $d(){var a=Sd;Sd=null;return a}function Zd(){Kd=Jd=Id=R=null;Nd=!1;Ld=null;Ud=0;V=Td=null}
function ae(a,b){return"function"===typeof b?b(a):b}function be(a,b,c){R=Vd();V=Xd();if(Md){var d=V.queue;b=d.dispatch;if(null!==Td&&(c=Td.get(d),void 0!==c)){Td.delete(d);d=V.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);V.memoizedState=d;return[d,b]}return[V.memoizedState,b]}a=a===ae?"function"===typeof b?b():b:void 0!==c?c(b):b;V.memoizedState=a;a=V.queue={last:null,dispatch:null};a=a.dispatch=ce.bind(null,R,a);return[V.memoizedState,a]}
function de(a,b){R=Vd();V=Xd();b=void 0===b?null:b;if(null!==V){var c=V.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Hd(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();V.memoizedState=[a,b];return a}
function ce(a,b,c){if(25<=Ud)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===R)if(Nd=!0,a={action:c,next:null},null===Td&&(Td=new Map),c=Td.get(b),void 0===c)Td.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function ee(){throw Error("startTransition cannot be called during server rendering.");}function fe(){throw Error("Cannot update optimistic state while rendering.");}
function ge(a){var b=Rd;Rd+=1;null===Sd&&(Sd=[]);return Dd(Sd,a,b)}function he(){throw Error("Cache cannot be refreshed during server rendering.");}function ie(){}
var me={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return ge(a);if(a.$$typeof===Dc||a.$$typeof===Xc)return a._currentValue2}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){Vd();return a._currentValue2},useMemo:de,useReducer:be,useRef:function(a){R=Vd();V=Xd();var b=V.memoizedState;return null===b?(a={current:a},V.memoizedState=a):b},useState:function(a){return be(ae,a)},
useInsertionEffect:ie,useLayoutEffect:ie,useCallback:function(a,b){return de(function(){return a},b)},useImperativeHandle:ie,useEffect:ie,useDebugValue:ie,useDeferredValue:function(a){Vd();return a},useTransition:function(){Vd();return[!1,ee]},useId:function(){var a=Id.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-xd(a)-1)).toString(32)+b;var c=je;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=Od++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return he},useHostTransitionStatus:function(){Vd();return La},useOptimistic:function(a){Vd();return[a,fe]},useFormState:function(a,b,c){Vd();var d=Pd++,e=Jd;if("function"===typeof a.$$FORM_ACTION){var f=null,g=Kd;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;
if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,null,d]),0),k===f&&(Qd=d,b=e[0]))}var l=a.bind(null,b);a=function(q){l(q)};"function"===typeof l.$$FORM_ACTION&&(a.$$FORM_ACTION=function(q){q=l.$$FORM_ACTION(q);void 0!==c&&(c+="",q.action=c);var m=q.data;m&&(null===f&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,null,d]),0)),m.append("$ACTION_KEY",f));return q});return[b,a]}var n=a.bind(null,b);return[b,function(q){n(q)}]}},je=null,ne=
{getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},oe=Ka.ReactCurrentDispatcher,pe=Ka.ReactCurrentCache;function qe(a){console.error(a);return null}function re(){}
function se(a,b,c,d,e,f,g,h,k,l,n,q){Ma.current=kb;var m=[],B=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:B,pingedTasks:m,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?qe:f,onPostpone:void 0===n?re:n,onAllReady:void 0===g?
re:g,onShellReady:void 0===h?re:h,onShellError:void 0===k?re:k,onFatalError:void 0===l?re:l,formState:void 0===q?null:q};c=te(b,0,null,d,!1,!1);c.parentFlushed=!0;a=ue(b,null,a,-1,null,c,B,null,d,kd,null,vd);m.push(a);return b}var O=null;function ve(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,we(a))}
function xe(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function ue(a,b,c,d,e,f,g,h,k,l,n,q){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var m={replay:null,node:c,childIndex:d,ping:function(){return ve(a,m)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:q,thenableState:b};g.add(m);return m}
function ye(a,b,c,d,e,f,g,h,k,l,n,q){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var m={replay:c,node:d,childIndex:e,ping:function(){return ve(a,m)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:q,thenableState:b};g.add(m);return m}function te(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function W(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function ze(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Ae(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((jd(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=r({},c,d)}b.legacyContext=e;Y(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Y(a,b,null,f,-1),b.keyPath=e}
function Be(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=wd(c,1,0),Z(a,b,d,-1),b.treeContext=c):h?Z(a,b,d,-1):Y(a,b,null,d,-1);b.keyPath=f}function Ce(a,b){if(a&&a.defaultProps){b=r({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function De(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=ld(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:d);ud(h,e,f,d);Ae(a,b,c,h,e)}else{h=ld(e,b.legacyContext);R={};Id=b;Jd=a;Kd=c;Pd=Od=0;Qd=-1;Rd=0;Sd=d;d=e(f,h);d=Yd(e,f,d,h);g=0!==Od;var k=Pd,l=Qd;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(ud(d,e,f,h),Ae(a,b,c,d,e)):Be(a,b,c,d,g,k,l)}else if("string"===typeof e)if(d=
b.blockedSegment,null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=ob(h,e,f),b.keyPath=c,Z(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Qb(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;k=b.keyPath;b.formatContext=ob(h,e,f);b.keyPath=c;Z(a,b,g,-1);b.formatContext=h;b.keyPath=k;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(Lb(e))}d.lastPushedText=!1}else{switch(e){case fd:case dd:case Ac:case Bc:case zc:e=b.keyPath;b.keyPath=c;Y(a,b,null,f.children,-1);b.keyPath=e;return;case ed:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Y(a,b,null,f.children,-1),b.keyPath=e);return;case $c:e=b.keyPath;b.keyPath=c;Y(a,b,null,f.children,-1);b.keyPath=e;return;case cd:throw Error("ReactDOMServer does not yet support scope components.");
case Zc:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Z(a,b,c,-1)}finally{b.keyPath=e}}else{l=b.keyPath;e=b.blockedBoundary;var n=b.blockedSegment;d=f.fallback;var q=f.children;f=new Set;g=xe(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);k=te(a,n.chunks.length,g,b.formatContext,!1,!1);n.children.push(k);n.lastPushedText=!1;var m=te(a,0,null,b.formatContext,!1,!1);m.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=m;a.renderState.boundaryResources=g.resources;b.keyPath=
c;try{if(Z(a,b,q,-1),a.renderState.generateStaticMarkup||m.lastPushedText&&m.textEmbedded&&m.chunks.push("\x3c!-- --\x3e"),m.status=1,Ee(g,m),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(B){m.status=4,g.status=4,h=W(a,B),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=n,b.keyPath=l}h=[c[0],"Suspense Fallback",c[2]];l=a.trackedPostpones;null!==l&&(n=[h[1],h[2],[],null],l.workingMap.set(h,n),5===g.status?l.workingMap.get(c)[4]=
n:g.trackedFallbackNode=n);b=ue(a,null,d,-1,e,k,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Yc:e=e.render;R={};Id=b;Jd=a;Kd=c;Pd=Od=0;Qd=-1;Rd=0;Sd=d;d=e(f,g);f=Yd(e,f,d,g);Be(a,b,c,f,0!==Od,Pd,Qd);return;case ad:e=e.type;f=Ce(e,f);De(a,b,c,d,e,f,g);return;case Cc:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue2;e._currentValue2=f;k=md;md=f={parent:k,depth:null===k?0:k.depth+1,
context:e,parentValue:g,value:f};b.context=f;b.keyPath=c;Y(a,b,null,h,-1);a=md;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue2=c===hd?a.context._defaultValue:c;a=md=a.parent;b.context=a;b.keyPath=d;return;case Dc:f=f.children;f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;Y(a,b,null,f,-1);b.keyPath=e;return;case bd:h=e._init;e=h(e._payload);f=Ce(e,f);De(a,b,c,d,e,f,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+
((null==e?e:typeof e)+"."));}}function Fe(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=te(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Z(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(Ee(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Y(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Fe(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case xc:var f=d.type,g=d.key,h=d.props,k=d.ref,l=jd(f),n=null==g?-1===e?0:e:g;g=[b.keyPath,l,n];if(null!==b.replay)a:{var q=b.replay;e=q.nodes;for(d=0;d<e.length;d++){var m=e[d];if(n===m[1]){if(4===m.length){if(null!==l&&l!==m[0])throw Error("Expected the resume to render <"+m[0]+"> in this slot but instead it rendered <"+
l+">. The tree doesn't match so React will fallback to client rendering.");l=m[2];m=m[3];n=b.node;b.replay={nodes:l,slots:m,pendingTasks:1};try{De(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(p){if("object"===typeof p&&null!==p&&(p===Bd||"function"===typeof p.then))throw b.node===n&&(b.replay=q),p;b.replay.pendingTasks--;
g=a;a=b.blockedBoundary;c=p;h=W(g,c);Ge(g,a,l,m,c,h)}b.replay=q}else{if(f!==Zc)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(jd(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{q=void 0;c=m[5];f=m[2];k=m[3];l=null===m[4]?[]:m[4][2];m=null===m[4]?null:m[4][3];n=b.keyPath;var B=b.replay,y=b.blockedBoundary,P=h.children;h=h.fallback;var t=new Set,w=xe(a,t);w.parentFlushed=!0;w.rootSegmentID=c;b.blockedBoundary=
w;b.replay={nodes:f,slots:k,pendingTasks:1};a.renderState.boundaryResources=w.resources;try{Z(a,b,P,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===w.pendingTasks&&0===w.status){w.status=1;a.completedBoundaries.push(w);break b}}catch(p){w.status=4,q=W(a,p),w.errorDigest=q,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(w)}finally{a.renderState.boundaryResources=
y?y.resources:null,b.blockedBoundary=y,b.replay=B,b.keyPath=n}b=ye(a,null,{nodes:l,slots:m,pendingTasks:0},h,-1,y,t,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else De(a,b,g,c,f,h,k);return;case yc:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");case bd:h=d._init;d=h(d._payload);Y(a,b,null,d,e);return}if(Ja(d)){He(a,
b,d,e);return}null===d||"object"!==typeof d?h=null:(h=id&&d[id]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);He(a,b,g,e)}return}if("function"===typeof d.then)return Y(a,b,null,ge(d),e);if(d.$$typeof===Dc||d.$$typeof===Xc)return Y(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+
"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=wc(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=wc(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function He(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{He(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(q){if("object"===typeof q&&
null!==q&&(q===Bd||"function"===typeof q.then))throw q;b.replay.pendingTasks--;c=a;var l=b.blockedBoundary,n=q;a=W(c,n);Ge(c,l,d,k,n,a)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)k=c[d],b.treeContext=wd(f,g,d),l=h[d],"number"===typeof l?(Fe(a,b,l,k,d),delete h[d]):Z(a,b,k,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=wd(f,g,h),Z(a,b,d,h);b.treeContext=
f;b.keyPath=e}
function Z(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.blockedSegment;if(null===l)try{return Y(a,b,null,c,d)}catch(m){if(Zd(),c=m===Bd?Fd():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=$d();a=ye(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;sd(g);return}}else{var n=
l.children.length,q=l.chunks.length;try{return Y(a,b,null,c,d)}catch(m){if(Zd(),l.children.length=n,l.chunks.length=q,c=m===Bd?Fd():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=$d();l=b.blockedSegment;n=te(a,l.chunks.length,null,b.formatContext,l.lastPushedText,!0);l.children.push(n);l.lastPushedText=!1;a=ue(a,d,b.node,b.childIndex,b.blockedBoundary,n,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;sd(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;sd(g);throw c;}function Ie(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,Je(this,b,a))}
function Ge(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Ge(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,n=xe(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=l;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var q in d)delete d[q]}}
function Ke(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(1!==b.status&&2!==b.status){a=a.replay;if(null===a){W(b,c);ze(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=W(b,c),Ge(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&(b.onShellError=re,a=b.onShellReady,a())}}else d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=W(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return Ke(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&(a=b.onAllReady,a())}function Ee(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&Ee(a,c)}else a.completedSegments.push(b)}
function Je(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&(a.onShellError=re,b=a.onShellReady,b())}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&Ee(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(Ie,
a),b.fallbackAbortableTasks.clear())):null!==c&&c.parentFlushed&&1===c.status&&(Ee(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&(a=a.onAllReady,a())}
function we(a){if(2!==a.status){var b=md,c=oe.current;oe.current=me;var d=pe.current;pe.current=ne;var e=O;O=a;var f=je;je=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,n=k.blockedBoundary;l.renderState.boundaryResources=n?n.resources:null;var q=k.blockedSegment;if(null===q){var m=l;if(0!==k.replay.pendingTasks){sd(k.context);try{var B=k.thenableState;k.thenableState=null;Y(m,k,B,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);Je(m,k.blockedBoundary,null)}catch(A){Zd();var y=A===Bd?Fd():A;if("object"===typeof y&&null!==y&&"function"===typeof y.then){var P=k.ping;y.then(P,P);k.thenableState=$d()}else{k.replay.pendingTasks--;k.abortSet.delete(k);l=void 0;var t=m,w=k.blockedBoundary,p=y,S=k.replay.nodes,C=k.replay.slots;l=W(t,p);Ge(t,w,S,C,p,l);m.pendingRootTasks--;if(0===m.pendingRootTasks){m.onShellError=re;var T=m.onShellReady;T()}m.allPendingTasks--;if(0===m.allPendingTasks){var D=
m.onAllReady;D()}}}finally{m.renderState.boundaryResources=null}}}else if(m=void 0,t=q,0===t.status){sd(k.context);var L=t.children.length,z=t.chunks.length;try{var ma=k.thenableState;k.thenableState=null;Y(l,k,ma,k.node,k.childIndex);l.renderState.generateStaticMarkup||t.lastPushedText&&t.textEmbedded&&t.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);t.status=1;Je(l,k.blockedBoundary,t)}catch(A){Zd();t.children.length=L;t.chunks.length=z;var U=A===Bd?Fd():A;if("object"===typeof U&&null!==U&&
"function"===typeof U.then){var aa=k.ping;U.then(aa,aa);k.thenableState=$d()}else{k.abortSet.delete(k);t.status=4;var M=k.blockedBoundary;m=W(l,U);null===M?ze(l,U):(M.pendingTasks--,4!==M.status&&(M.status=4,M.errorDigest=m,M.parentFlushed&&l.clientRenderedBoundaries.push(M)));l.allPendingTasks--;if(0===l.allPendingTasks){var X=l.onAllReady;X()}}}finally{l.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&Le(a,a.destination)}catch(A){W(a,A),ze(a,A)}finally{je=f,oe.current=c,
pe.current=d,c===me&&sd(b),O=e}}}
function Me(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=Ne(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function Ne(a,b,c){var d=c.boundary;if(null===d)return Me(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=v(d),b.push(d),b.push('"')),b.push("></template>")),Me(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),$b(b,a.renderState,
d.rootSegmentID),Me(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),$b(b,a.renderState,d.rootSegmentID),Me(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(tc,e),c.stylesheets.forEach(uc,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");
Ne(a,b,c[0]);a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e");return a}function Oe(a,b,c){ac(b,a.renderState,c.parentFormatContext,c.id);Ne(a,b,c);return bc(b,c.parentFormatContext)}
function Pe(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)Qe(a,b,c,d[e]);d.length=0;kc(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),qc(b,c)):(b.push('" data-sty="'),rc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Sb(b,a)&&d}
function Qe(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return Oe(a,b,d)}if(e===c.rootSegmentID)return Oe(a,b,d);Oe(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function Le(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,n=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(n)for(f=0;f<n.length;f++)b.push(n[f]);else{var q=J("head");b.push(q);
b.push(">")}}else if(n)for(f=0;f<n.length;f++)b.push(n[f]);var m=e.charsetChunks;for(f=0;f<m.length;f++)b.push(m[f]);m.length=0;e.preconnects.forEach(K,b);e.preconnects.clear();var B=e.preconnectChunks;for(f=0;f<B.length;f++)b.push(B[f]);B.length=0;e.fontPreloads.forEach(K,b);e.fontPreloads.clear();e.highImagePreloads.forEach(K,b);e.highImagePreloads.clear();e.styles.forEach(nc,b);var y=e.importMapChunks;for(f=0;f<y.length;f++)b.push(y[f]);y.length=0;e.bootstrapScripts.forEach(K,b);e.scripts.forEach(K,
b);e.scripts.clear();e.bulkPreloads.forEach(K,b);e.bulkPreloads.clear();var P=e.preloadChunks;for(f=0;f<P.length;f++)b.push(P[f]);P.length=0;var t=e.hoistableChunks;for(f=0;f<t.length;f++)b.push(t[f]);t.length=0;if(l&&null===n){var w=Lb("head");b.push(w)}Ne(a,b,d);a.completedRootSegment=null;Sb(b,a.renderState)}else return;var p=a.renderState;d=0;p.preconnects.forEach(K,b);p.preconnects.clear();var S=p.preconnectChunks;for(d=0;d<S.length;d++)b.push(S[d]);S.length=0;p.fontPreloads.forEach(K,b);p.fontPreloads.clear();
p.highImagePreloads.forEach(K,b);p.highImagePreloads.clear();p.styles.forEach(pc,b);p.scripts.forEach(K,b);p.scripts.clear();p.bulkPreloads.forEach(K,b);p.bulkPreloads.clear();var C=p.preloadChunks;for(d=0;d<C.length;d++)b.push(C[d]);C.length=0;var T=p.hoistableChunks;for(d=0;d<T.length;d++)b.push(T[d]);T.length=0;var D=a.clientRenderedBoundaries;for(c=0;c<D.length;c++){var L=D[c];p=b;var z=a.resumableState,ma=a.renderState,U=L.rootSegmentID,aa=L.errorDigest,M=L.errorMessage,X=L.errorComponentStack,
A=0===z.streamingFormat;A?(p.push(ma.startInlineScript),0===(z.instructions&4)?(z.instructions|=4,p.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):p.push('$RX("')):p.push('<template data-rxi="" data-bid="');p.push(ma.boundaryPrefix);var Na=U.toString(16);p.push(Na);A&&p.push('"');if(aa||M||X)if(A){p.push(",");var Oa=dc(aa||"");p.push(Oa)}else{p.push('" data-dgst="');
var Pa=v(aa||"");p.push(Pa)}if(M||X)if(A){p.push(",");var na=dc(M||"");p.push(na)}else{p.push('" data-msg="');var Q=v(M||"");p.push(Q)}if(X)if(A){p.push(",");var pb=dc(X);p.push(pb)}else{p.push('" data-stck="');var oa=v(X);p.push(oa)}if(A?!p.push(")\x3c/script>"):!p.push('"></template>')){a.destination=null;c++;D.splice(0,c);return}}D.splice(0,c);var pa=a.completedBoundaries;for(c=0;c<pa.length;c++)if(!Pe(a,b,pa[c])){a.destination=null;c++;pa.splice(0,c);return}pa.splice(0,c);var ba=a.partialBoundaries;
for(c=0;c<ba.length;c++){var qa=ba[c];a:{D=a;L=b;D.renderState.boundaryResources=qa.resources;var ra=qa.completedSegments;for(z=0;z<ra.length;z++)if(!Qe(D,L,qa,ra[z])){z++;ra.splice(0,z);var Qa=!1;break a}ra.splice(0,z);Qa=kc(L,qa.resources,D.renderState)}if(!Qa){a.destination=null;c++;ba.splice(0,c);return}}ba.splice(0,c);var sa=a.completedBoundaries;for(c=0;c<sa.length;c++)if(!Pe(a,b,sa[c])){a.destination=null;c++;sa.splice(0,c);return}sa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&
0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(ba=Lb("body"),b.push(ba)),c.hasHtml&&(c=Lb("html"),b.push(c)),b.push(null),a.destination=null)}}function sc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?Le(a,b):a.flushScheduled=!1}}
function Re(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{Le(a,b)}catch(c){W(a,c),ze(a,c)}}}function Se(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return Ke(e,a,d)});c.clear()}null!==a.destination&&Le(a,a.destination)}catch(e){W(a,e),ze(a,e)}}function Te(){}
function Ue(a,b,c,d){var e=!1,f=null,g="",h=!1;b=mb(b?b.identifierPrefix:void 0,void 0);a=se(a,b,vc(b,c),nb(),Infinity,Te,void 0,function(){h=!0},void 0,void 0,void 0);a.flushScheduled=null!==a.destination;we(a);Se(a,d);Re(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");
return g}function Ve(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;a.__proto__=b}var We=function(a){function b(){var d=a.call(this,{})||this;d.request=null;d.startedFlowing=!1;return d}Ve(b,a);var c=b.prototype;c._destroy=function(d,e){Se(this.request);e(d)};c._read=function(){this.startedFlowing&&Re(this.request,this)};return b}(ia.Readable);function Xe(){}
function Ye(a,b){var c=new We;b=mb(b?b.identifierPrefix:void 0,void 0);var d=se(a,b,vc(b,!1),nb(),Infinity,Xe,function(){c.startedFlowing=!0;Re(d,c)},void 0,void 0,void 0);c.request=d;d.flushScheduled=null!==d.destination;we(d);return c}exports.renderToNodeStream=function(a,b){return Ye(a,b)};exports.renderToStaticMarkup=function(a,b){return Ue(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.renderToStaticNodeStream=function(a,b){return Ye(a,b)};exports.renderToString=function(a,b){return Ue(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.version="18.3.0-canary-8c8ee9ee6-20231026";
