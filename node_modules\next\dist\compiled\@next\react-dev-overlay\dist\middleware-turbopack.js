(function(){var e={535:function(e,t,r){"use strict";e=r.nmd(e);const n=r(54);const wrapAnsi16=(e,t)=>function(){const r=e.apply(n,arguments);return`[${r+t}m`};const wrapAnsi256=(e,t)=>function(){const r=e.apply(n,arguments);return`[${38+t};5;${r}m`};const wrapAnsi16m=(e,t)=>function(){const r=e.apply(n,arguments);return`[${38+t};2;${r[0]};${r[1]};${r[2]}m`};function assembleStyles(){const e=new Map;const t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};t.color.grey=t.color.gray;for(const r of Object.keys(t)){const n=t[r];for(const r of Object.keys(n)){const u=n[r];t[r]={open:`[${u[0]}m`,close:`[${u[1]}m`};n[r]=t[r];e.set(u[0],u[1])}Object.defineProperty(t,r,{value:n,enumerable:false});Object.defineProperty(t,"codes",{value:e,enumerable:false})}const ansi2ansi=e=>e;const rgb2rgb=(e,t,r)=>[e,t,r];t.color.close="[39m";t.bgColor.close="[49m";t.color.ansi={ansi:wrapAnsi16(ansi2ansi,0)};t.color.ansi256={ansi256:wrapAnsi256(ansi2ansi,0)};t.color.ansi16m={rgb:wrapAnsi16m(rgb2rgb,0)};t.bgColor.ansi={ansi:wrapAnsi16(ansi2ansi,10)};t.bgColor.ansi256={ansi256:wrapAnsi256(ansi2ansi,10)};t.bgColor.ansi16m={rgb:wrapAnsi16m(rgb2rgb,10)};for(let e of Object.keys(n)){if(typeof n[e]!=="object"){continue}const r=n[e];if(e==="ansi16"){e="ansi"}if("ansi16"in r){t.color.ansi[e]=wrapAnsi16(r.ansi16,0);t.bgColor.ansi[e]=wrapAnsi16(r.ansi16,10)}if("ansi256"in r){t.color.ansi256[e]=wrapAnsi256(r.ansi256,0);t.bgColor.ansi256[e]=wrapAnsi256(r.ansi256,10)}if("rgb"in r){t.color.ansi16m[e]=wrapAnsi16m(r.rgb,0);t.bgColor.ansi16m[e]=wrapAnsi16m(r.rgb,10)}}return t}Object.defineProperty(e,"exports",{enumerable:true,get:assembleStyles})},14:function(e,t,r){"use strict";e=r.nmd(e);const wrapAnsi16=(e,t)=>(...r)=>{const n=e(...r);return`[${n+t}m`};const wrapAnsi256=(e,t)=>(...r)=>{const n=e(...r);return`[${38+t};5;${n}m`};const wrapAnsi16m=(e,t)=>(...r)=>{const n=e(...r);return`[${38+t};2;${n[0]};${n[1]};${n[2]}m`};const ansi2ansi=e=>e;const rgb2rgb=(e,t,r)=>[e,t,r];const setLazyProperty=(e,t,r)=>{Object.defineProperty(e,t,{get:()=>{const n=r();Object.defineProperty(e,t,{value:n,enumerable:true,configurable:true});return n},enumerable:true,configurable:true})};let n;const makeDynamicStyles=(e,t,u,o)=>{if(n===undefined){n=r(226)}const s=o?10:0;const a={};for(const[r,o]of Object.entries(n)){const n=r==="ansi16"?"ansi":r;if(r===t){a[n]=e(u,s)}else if(typeof o==="object"){a[n]=e(o[t],s)}}return a};function assembleStyles(){const e=new Map;const t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};t.color.gray=t.color.blackBright;t.bgColor.bgGray=t.bgColor.bgBlackBright;t.color.grey=t.color.blackBright;t.bgColor.bgGrey=t.bgColor.bgBlackBright;for(const[r,n]of Object.entries(t)){for(const[r,u]of Object.entries(n)){t[r]={open:`[${u[0]}m`,close:`[${u[1]}m`};n[r]=t[r];e.set(u[0],u[1])}Object.defineProperty(t,r,{value:n,enumerable:false})}Object.defineProperty(t,"codes",{value:e,enumerable:false});t.color.close="[39m";t.bgColor.close="[49m";setLazyProperty(t.color,"ansi",(()=>makeDynamicStyles(wrapAnsi16,"ansi16",ansi2ansi,false)));setLazyProperty(t.color,"ansi256",(()=>makeDynamicStyles(wrapAnsi256,"ansi256",ansi2ansi,false)));setLazyProperty(t.color,"ansi16m",(()=>makeDynamicStyles(wrapAnsi16m,"rgb",rgb2rgb,false)));setLazyProperty(t.bgColor,"ansi",(()=>makeDynamicStyles(wrapAnsi16,"ansi16",ansi2ansi,true)));setLazyProperty(t.bgColor,"ansi256",(()=>makeDynamicStyles(wrapAnsi256,"ansi256",ansi2ansi,true)));setLazyProperty(t.bgColor,"ansi16m",(()=>makeDynamicStyles(wrapAnsi16m,"rgb",rgb2rgb,true)));return t}Object.defineProperty(e,"exports",{enumerable:true,get:assembleStyles})},148:function(e,t,r){"use strict";const n=r(379);const u=r(535);const o=r(220).stdout;const s=r(299);const a=process.platform==="win32"&&!(process.env.TERM||"").toLowerCase().startsWith("xterm");const i=["ansi","ansi","ansi256","ansi16m"];const l=new Set(["gray"]);const c=Object.create(null);function applyOptions(e,t){t=t||{};const r=o?o.level:0;e.level=t.level===undefined?r:t.level;e.enabled="enabled"in t?t.enabled:e.level>0}function Chalk(e){if(!this||!(this instanceof Chalk)||this.template){const t={};applyOptions(t,e);t.template=function(){const e=[].slice.call(arguments);return chalkTag.apply(null,[t.template].concat(e))};Object.setPrototypeOf(t,Chalk.prototype);Object.setPrototypeOf(t.template,t);t.template.constructor=Chalk;return t.template}applyOptions(this,e)}if(a){u.blue.open="[94m"}for(const e of Object.keys(u)){u[e].closeRe=new RegExp(n(u[e].close),"g");c[e]={get(){const t=u[e];return build.call(this,this._styles?this._styles.concat(t):[t],this._empty,e)}}}c.visible={get(){return build.call(this,this._styles||[],true,"visible")}};u.color.closeRe=new RegExp(n(u.color.close),"g");for(const e of Object.keys(u.color.ansi)){if(l.has(e)){continue}c[e]={get(){const t=this.level;return function(){const r=u.color[i[t]][e].apply(null,arguments);const n={open:r,close:u.color.close,closeRe:u.color.closeRe};return build.call(this,this._styles?this._styles.concat(n):[n],this._empty,e)}}}}u.bgColor.closeRe=new RegExp(n(u.bgColor.close),"g");for(const e of Object.keys(u.bgColor.ansi)){if(l.has(e)){continue}const t="bg"+e[0].toUpperCase()+e.slice(1);c[t]={get(){const t=this.level;return function(){const r=u.bgColor[i[t]][e].apply(null,arguments);const n={open:r,close:u.bgColor.close,closeRe:u.bgColor.closeRe};return build.call(this,this._styles?this._styles.concat(n):[n],this._empty,e)}}}}const f=Object.defineProperties((()=>{}),c);function build(e,t,r){const builder=function(){return applyStyle.apply(builder,arguments)};builder._styles=e;builder._empty=t;const n=this;Object.defineProperty(builder,"level",{enumerable:true,get(){return n.level},set(e){n.level=e}});Object.defineProperty(builder,"enabled",{enumerable:true,get(){return n.enabled},set(e){n.enabled=e}});builder.hasGrey=this.hasGrey||r==="gray"||r==="grey";builder.__proto__=f;return builder}function applyStyle(){const e=arguments;const t=e.length;let r=String(arguments[0]);if(t===0){return""}if(t>1){for(let n=1;n<t;n++){r+=" "+e[n]}}if(!this.enabled||this.level<=0||!r){return this._empty?"":r}const n=u.dim.open;if(a&&this.hasGrey){u.dim.open=""}for(const e of this._styles.slice().reverse()){r=e.open+r.replace(e.closeRe,e.open)+e.close;r=r.replace(/\r?\n/g,`${e.close}$&${e.open}`)}u.dim.open=n;return r}function chalkTag(e,t){if(!Array.isArray(t)){return[].slice.call(arguments,1).join(" ")}const r=[].slice.call(arguments,2);const n=[t.raw[0]];for(let e=1;e<t.length;e++){n.push(String(r[e-1]).replace(/[{}\\]/g,"\\$&"));n.push(String(t.raw[e]))}return s(e,n.join(""))}Object.defineProperties(Chalk.prototype,c);e.exports=Chalk();e.exports.supportsColor=o;e.exports["default"]=e.exports},299:function(e){"use strict";const t=/(?:\\(u[a-f\d]{4}|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi;const r=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g;const n=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/;const u=/\\(u[a-f\d]{4}|x[a-f\d]{2}|.)|([^\\])/gi;const o=new Map([["n","\n"],["r","\r"],["t","\t"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e",""],["a",""]]);function unescape(e){if(e[0]==="u"&&e.length===5||e[0]==="x"&&e.length===3){return String.fromCharCode(parseInt(e.slice(1),16))}return o.get(e)||e}function parseArguments(e,t){const r=[];const o=t.trim().split(/\s*,\s*/g);let s;for(const t of o){if(!isNaN(t)){r.push(Number(t))}else if(s=t.match(n)){r.push(s[2].replace(u,((e,t,r)=>t?unescape(t):r)))}else{throw new Error(`Invalid Chalk template style argument: ${t} (in style '${e}')`)}}return r}function parseStyle(e){r.lastIndex=0;const t=[];let n;while((n=r.exec(e))!==null){const e=n[1];if(n[2]){const r=parseArguments(e,n[2]);t.push([e].concat(r))}else{t.push([e])}}return t}function buildStyle(e,t){const r={};for(const e of t){for(const t of e.styles){r[t[0]]=e.inverse?null:t.slice(1)}}let n=e;for(const e of Object.keys(r)){if(Array.isArray(r[e])){if(!(e in n)){throw new Error(`Unknown Chalk style: ${e}`)}if(r[e].length>0){n=n[e].apply(n,r[e])}else{n=n[e]}}}return n}e.exports=(e,r)=>{const n=[];const u=[];let o=[];r.replace(t,((t,r,s,a,i,l)=>{if(r){o.push(unescape(r))}else if(a){const t=o.join("");o=[];u.push(n.length===0?t:buildStyle(e,n)(t));n.push({inverse:s,styles:parseStyle(a)})}else if(i){if(n.length===0){throw new Error("Found extraneous } in Chalk template literal")}u.push(buildStyle(e,n)(o.join("")));o=[];n.pop()}else{o.push(l)}}));u.push(o.join(""));if(n.length>0){const e=`Chalk template literal is missing ${n.length} closing bracket${n.length===1?"":"s"} (\`}\`)`;throw new Error(e)}return u.join("")}},802:function(e,t,r){"use strict";const n=r(14);const{stdout:u,stderr:o}=r(793);const{stringReplaceAll:s,stringEncaseCRLFWithFirstIndex:a}=r(203);const i=["ansi","ansi","ansi256","ansi16m"];const l=Object.create(null);const applyOptions=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3)){throw new Error("The `level` option should be an integer from 0 to 3")}const r=u?u.level:0;e.level=t.level===undefined?r:t.level};class ChalkClass{constructor(e){return chalkFactory(e)}}const chalkFactory=e=>{const t={};applyOptions(t,e);t.template=(...e)=>chalkTag(t.template,...e);Object.setPrototypeOf(t,Chalk.prototype);Object.setPrototypeOf(t.template,t);t.template.constructor=()=>{throw new Error("`chalk.constructor()` is deprecated. Use `new chalk.Instance()` instead.")};t.template.Instance=ChalkClass;return t.template};function Chalk(e){return chalkFactory(e)}for(const[e,t]of Object.entries(n)){l[e]={get(){const r=createBuilder(this,createStyler(t.open,t.close,this._styler),this._isEmpty);Object.defineProperty(this,e,{value:r});return r}}}l.visible={get(){const e=createBuilder(this,this._styler,true);Object.defineProperty(this,"visible",{value:e});return e}};const c=["rgb","hex","keyword","hsl","hsv","hwb","ansi","ansi256"];for(const e of c){l[e]={get(){const{level:t}=this;return function(...r){const u=createStyler(n.color[i[t]][e](...r),n.color.close,this._styler);return createBuilder(this,u,this._isEmpty)}}}}for(const e of c){const t="bg"+e[0].toUpperCase()+e.slice(1);l[t]={get(){const{level:t}=this;return function(...r){const u=createStyler(n.bgColor[i[t]][e](...r),n.bgColor.close,this._styler);return createBuilder(this,u,this._isEmpty)}}}}const f=Object.defineProperties((()=>{}),{...l,level:{enumerable:true,get(){return this._generator.level},set(e){this._generator.level=e}}});const createStyler=(e,t,r)=>{let n;let u;if(r===undefined){n=e;u=t}else{n=r.openAll+e;u=t+r.closeAll}return{open:e,close:t,openAll:n,closeAll:u,parent:r}};const createBuilder=(e,t,r)=>{const builder=(...e)=>applyStyle(builder,e.length===1?""+e[0]:e.join(" "));Object.setPrototypeOf(builder,f);builder._generator=e;builder._styler=t;builder._isEmpty=r;return builder};const applyStyle=(e,t)=>{if(e.level<=0||!t){return e._isEmpty?"":t}let r=e._styler;if(r===undefined){return t}const{openAll:n,closeAll:u}=r;if(t.indexOf("")!==-1){while(r!==undefined){t=s(t,r.close,r.open);r=r.parent}}const o=t.indexOf("\n");if(o!==-1){t=a(t,u,n,o)}return n+t+u};let p;const chalkTag=(e,...t)=>{const[n]=t;if(!Array.isArray(n)){return t.join(" ")}const u=t.slice(1);const o=[n.raw[0]];for(let e=1;e<n.length;e++){o.push(String(u[e-1]).replace(/[{}\\]/g,"\\$&"),String(n.raw[e]))}if(p===undefined){p=r(209)}return p(e,o.join(""))};Object.defineProperties(Chalk.prototype,l);const h=Chalk();h.supportsColor=u;h.stderr=Chalk({level:o?o.level:0});h.stderr.supportsColor=o;e.exports=h},209:function(e){"use strict";const t=/(?:\\(u(?:[a-f\d]{4}|\{[a-f\d]{1,6}\})|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi;const r=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g;const n=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/;const u=/\\(u(?:[a-f\d]{4}|{[a-f\d]{1,6}})|x[a-f\d]{2}|.)|([^\\])/gi;const o=new Map([["n","\n"],["r","\r"],["t","\t"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e",""],["a",""]]);function unescape(e){const t=e[0]==="u";const r=e[1]==="{";if(t&&!r&&e.length===5||e[0]==="x"&&e.length===3){return String.fromCharCode(parseInt(e.slice(1),16))}if(t&&r){return String.fromCodePoint(parseInt(e.slice(2,-1),16))}return o.get(e)||e}function parseArguments(e,t){const r=[];const o=t.trim().split(/\s*,\s*/g);let s;for(const t of o){const o=Number(t);if(!Number.isNaN(o)){r.push(o)}else if(s=t.match(n)){r.push(s[2].replace(u,((e,t,r)=>t?unescape(t):r)))}else{throw new Error(`Invalid Chalk template style argument: ${t} (in style '${e}')`)}}return r}function parseStyle(e){r.lastIndex=0;const t=[];let n;while((n=r.exec(e))!==null){const e=n[1];if(n[2]){const r=parseArguments(e,n[2]);t.push([e].concat(r))}else{t.push([e])}}return t}function buildStyle(e,t){const r={};for(const e of t){for(const t of e.styles){r[t[0]]=e.inverse?null:t.slice(1)}}let n=e;for(const[e,t]of Object.entries(r)){if(!Array.isArray(t)){continue}if(!(e in n)){throw new Error(`Unknown Chalk style: ${e}`)}n=t.length>0?n[e](...t):n[e]}return n}e.exports=(e,r)=>{const n=[];const u=[];let o=[];r.replace(t,((t,r,s,a,i,l)=>{if(r){o.push(unescape(r))}else if(a){const t=o.join("");o=[];u.push(n.length===0?t:buildStyle(e,n)(t));n.push({inverse:s,styles:parseStyle(a)})}else if(i){if(n.length===0){throw new Error("Found extraneous } in Chalk template literal")}u.push(buildStyle(e,n)(o.join("")));o=[];n.pop()}else{o.push(l)}}));u.push(o.join(""));if(n.length>0){const e=`Chalk template literal is missing ${n.length} closing bracket${n.length===1?"":"s"} (\`}\`)`;throw new Error(e)}return u.join("")}},203:function(e){"use strict";const stringReplaceAll=(e,t,r)=>{let n=e.indexOf(t);if(n===-1){return e}const u=t.length;let o=0;let s="";do{s+=e.substr(o,n-o)+t+r;o=n+u;n=e.indexOf(t,o)}while(n!==-1);s+=e.substr(o);return s};const stringEncaseCRLFWithFirstIndex=(e,t,r,n)=>{let u=0;let o="";do{const s=e[n-1]==="\r";o+=e.substr(u,(s?n-1:n)-u)+t+(s?"\r\n":"\n")+r;u=n+1;n=e.indexOf("\n",u)}while(n!==-1);o+=e.substr(u);return o};e.exports={stringReplaceAll:stringReplaceAll,stringEncaseCRLFWithFirstIndex:stringEncaseCRLFWithFirstIndex}},117:function(e,t,r){var n=r(251);var u={};for(var o in n){if(n.hasOwnProperty(o)){u[n[o]]=o}}var s=e.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var a in s){if(s.hasOwnProperty(a)){if(!("channels"in s[a])){throw new Error("missing channels property: "+a)}if(!("labels"in s[a])){throw new Error("missing channel labels property: "+a)}if(s[a].labels.length!==s[a].channels){throw new Error("channel and label counts mismatch: "+a)}var i=s[a].channels;var l=s[a].labels;delete s[a].channels;delete s[a].labels;Object.defineProperty(s[a],"channels",{value:i});Object.defineProperty(s[a],"labels",{value:l})}}s.rgb.hsl=function(e){var t=e[0]/255;var r=e[1]/255;var n=e[2]/255;var u=Math.min(t,r,n);var o=Math.max(t,r,n);var s=o-u;var a;var i;var l;if(o===u){a=0}else if(t===o){a=(r-n)/s}else if(r===o){a=2+(n-t)/s}else if(n===o){a=4+(t-r)/s}a=Math.min(a*60,360);if(a<0){a+=360}l=(u+o)/2;if(o===u){i=0}else if(l<=.5){i=s/(o+u)}else{i=s/(2-o-u)}return[a,i*100,l*100]};s.rgb.hsv=function(e){var t;var r;var n;var u;var o;var s=e[0]/255;var a=e[1]/255;var i=e[2]/255;var l=Math.max(s,a,i);var c=l-Math.min(s,a,i);var diffc=function(e){return(l-e)/6/c+1/2};if(c===0){u=o=0}else{o=c/l;t=diffc(s);r=diffc(a);n=diffc(i);if(s===l){u=n-r}else if(a===l){u=1/3+t-n}else if(i===l){u=2/3+r-t}if(u<0){u+=1}else if(u>1){u-=1}}return[u*360,o*100,l*100]};s.rgb.hwb=function(e){var t=e[0];var r=e[1];var n=e[2];var u=s.rgb.hsl(e)[0];var o=1/255*Math.min(t,Math.min(r,n));n=1-1/255*Math.max(t,Math.max(r,n));return[u,o*100,n*100]};s.rgb.cmyk=function(e){var t=e[0]/255;var r=e[1]/255;var n=e[2]/255;var u;var o;var s;var a;a=Math.min(1-t,1-r,1-n);u=(1-t-a)/(1-a)||0;o=(1-r-a)/(1-a)||0;s=(1-n-a)/(1-a)||0;return[u*100,o*100,s*100,a*100]};function comparativeDistance(e,t){return Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)+Math.pow(e[2]-t[2],2)}s.rgb.keyword=function(e){var t=u[e];if(t){return t}var r=Infinity;var o;for(var s in n){if(n.hasOwnProperty(s)){var a=n[s];var i=comparativeDistance(e,a);if(i<r){r=i;o=s}}}return o};s.keyword.rgb=function(e){return n[e]};s.rgb.xyz=function(e){var t=e[0]/255;var r=e[1]/255;var n=e[2]/255;t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92;r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92;n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92;var u=t*.4124+r*.3576+n*.1805;var o=t*.2126+r*.7152+n*.0722;var s=t*.0193+r*.1192+n*.9505;return[u*100,o*100,s*100]};s.rgb.lab=function(e){var t=s.rgb.xyz(e);var r=t[0];var n=t[1];var u=t[2];var o;var a;var i;r/=95.047;n/=100;u/=108.883;r=r>.008856?Math.pow(r,1/3):7.787*r+16/116;n=n>.008856?Math.pow(n,1/3):7.787*n+16/116;u=u>.008856?Math.pow(u,1/3):7.787*u+16/116;o=116*n-16;a=500*(r-n);i=200*(n-u);return[o,a,i]};s.hsl.rgb=function(e){var t=e[0]/360;var r=e[1]/100;var n=e[2]/100;var u;var o;var s;var a;var i;if(r===0){i=n*255;return[i,i,i]}if(n<.5){o=n*(1+r)}else{o=n+r-n*r}u=2*n-o;a=[0,0,0];for(var l=0;l<3;l++){s=t+1/3*-(l-1);if(s<0){s++}if(s>1){s--}if(6*s<1){i=u+(o-u)*6*s}else if(2*s<1){i=o}else if(3*s<2){i=u+(o-u)*(2/3-s)*6}else{i=u}a[l]=i*255}return a};s.hsl.hsv=function(e){var t=e[0];var r=e[1]/100;var n=e[2]/100;var u=r;var o=Math.max(n,.01);var s;var a;n*=2;r*=n<=1?n:2-n;u*=o<=1?o:2-o;a=(n+r)/2;s=n===0?2*u/(o+u):2*r/(n+r);return[t,s*100,a*100]};s.hsv.rgb=function(e){var t=e[0]/60;var r=e[1]/100;var n=e[2]/100;var u=Math.floor(t)%6;var o=t-Math.floor(t);var s=255*n*(1-r);var a=255*n*(1-r*o);var i=255*n*(1-r*(1-o));n*=255;switch(u){case 0:return[n,i,s];case 1:return[a,n,s];case 2:return[s,n,i];case 3:return[s,a,n];case 4:return[i,s,n];case 5:return[n,s,a]}};s.hsv.hsl=function(e){var t=e[0];var r=e[1]/100;var n=e[2]/100;var u=Math.max(n,.01);var o;var s;var a;a=(2-r)*n;o=(2-r)*u;s=r*u;s/=o<=1?o:2-o;s=s||0;a/=2;return[t,s*100,a*100]};s.hwb.rgb=function(e){var t=e[0]/360;var r=e[1]/100;var n=e[2]/100;var u=r+n;var o;var s;var a;var i;if(u>1){r/=u;n/=u}o=Math.floor(6*t);s=1-n;a=6*t-o;if((o&1)!==0){a=1-a}i=r+a*(s-r);var l;var c;var f;switch(o){default:case 6:case 0:l=s;c=i;f=r;break;case 1:l=i;c=s;f=r;break;case 2:l=r;c=s;f=i;break;case 3:l=r;c=i;f=s;break;case 4:l=i;c=r;f=s;break;case 5:l=s;c=r;f=i;break}return[l*255,c*255,f*255]};s.cmyk.rgb=function(e){var t=e[0]/100;var r=e[1]/100;var n=e[2]/100;var u=e[3]/100;var o;var s;var a;o=1-Math.min(1,t*(1-u)+u);s=1-Math.min(1,r*(1-u)+u);a=1-Math.min(1,n*(1-u)+u);return[o*255,s*255,a*255]};s.xyz.rgb=function(e){var t=e[0]/100;var r=e[1]/100;var n=e[2]/100;var u;var o;var s;u=t*3.2406+r*-1.5372+n*-.4986;o=t*-.9689+r*1.8758+n*.0415;s=t*.0557+r*-.204+n*1.057;u=u>.0031308?1.055*Math.pow(u,1/2.4)-.055:u*12.92;o=o>.0031308?1.055*Math.pow(o,1/2.4)-.055:o*12.92;s=s>.0031308?1.055*Math.pow(s,1/2.4)-.055:s*12.92;u=Math.min(Math.max(0,u),1);o=Math.min(Math.max(0,o),1);s=Math.min(Math.max(0,s),1);return[u*255,o*255,s*255]};s.xyz.lab=function(e){var t=e[0];var r=e[1];var n=e[2];var u;var o;var s;t/=95.047;r/=100;n/=108.883;t=t>.008856?Math.pow(t,1/3):7.787*t+16/116;r=r>.008856?Math.pow(r,1/3):7.787*r+16/116;n=n>.008856?Math.pow(n,1/3):7.787*n+16/116;u=116*r-16;o=500*(t-r);s=200*(r-n);return[u,o,s]};s.lab.xyz=function(e){var t=e[0];var r=e[1];var n=e[2];var u;var o;var s;o=(t+16)/116;u=r/500+o;s=o-n/200;var a=Math.pow(o,3);var i=Math.pow(u,3);var l=Math.pow(s,3);o=a>.008856?a:(o-16/116)/7.787;u=i>.008856?i:(u-16/116)/7.787;s=l>.008856?l:(s-16/116)/7.787;u*=95.047;o*=100;s*=108.883;return[u,o,s]};s.lab.lch=function(e){var t=e[0];var r=e[1];var n=e[2];var u;var o;var s;u=Math.atan2(n,r);o=u*360/2/Math.PI;if(o<0){o+=360}s=Math.sqrt(r*r+n*n);return[t,s,o]};s.lch.lab=function(e){var t=e[0];var r=e[1];var n=e[2];var u;var o;var s;s=n/360*2*Math.PI;u=r*Math.cos(s);o=r*Math.sin(s);return[t,u,o]};s.rgb.ansi16=function(e){var t=e[0];var r=e[1];var n=e[2];var u=1 in arguments?arguments[1]:s.rgb.hsv(e)[2];u=Math.round(u/50);if(u===0){return 30}var o=30+(Math.round(n/255)<<2|Math.round(r/255)<<1|Math.round(t/255));if(u===2){o+=60}return o};s.hsv.ansi16=function(e){return s.rgb.ansi16(s.hsv.rgb(e),e[2])};s.rgb.ansi256=function(e){var t=e[0];var r=e[1];var n=e[2];if(t===r&&r===n){if(t<8){return 16}if(t>248){return 231}return Math.round((t-8)/247*24)+232}var u=16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5);return u};s.ansi16.rgb=function(e){var t=e%10;if(t===0||t===7){if(e>50){t+=3.5}t=t/10.5*255;return[t,t,t]}var r=(~~(e>50)+1)*.5;var n=(t&1)*r*255;var u=(t>>1&1)*r*255;var o=(t>>2&1)*r*255;return[n,u,o]};s.ansi256.rgb=function(e){if(e>=232){var t=(e-232)*10+8;return[t,t,t]}e-=16;var r;var n=Math.floor(e/36)/5*255;var u=Math.floor((r=e%36)/6)/5*255;var o=r%6/5*255;return[n,u,o]};s.rgb.hex=function(e){var t=((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255);var r=t.toString(16).toUpperCase();return"000000".substring(r.length)+r};s.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t){return[0,0,0]}var r=t[0];if(t[0].length===3){r=r.split("").map((function(e){return e+e})).join("")}var n=parseInt(r,16);var u=n>>16&255;var o=n>>8&255;var s=n&255;return[u,o,s]};s.rgb.hcg=function(e){var t=e[0]/255;var r=e[1]/255;var n=e[2]/255;var u=Math.max(Math.max(t,r),n);var o=Math.min(Math.min(t,r),n);var s=u-o;var a;var i;if(s<1){a=o/(1-s)}else{a=0}if(s<=0){i=0}else if(u===t){i=(r-n)/s%6}else if(u===r){i=2+(n-t)/s}else{i=4+(t-r)/s+4}i/=6;i%=1;return[i*360,s*100,a*100]};s.hsl.hcg=function(e){var t=e[1]/100;var r=e[2]/100;var n=1;var u=0;if(r<.5){n=2*t*r}else{n=2*t*(1-r)}if(n<1){u=(r-.5*n)/(1-n)}return[e[0],n*100,u*100]};s.hsv.hcg=function(e){var t=e[1]/100;var r=e[2]/100;var n=t*r;var u=0;if(n<1){u=(r-n)/(1-n)}return[e[0],n*100,u*100]};s.hcg.rgb=function(e){var t=e[0]/360;var r=e[1]/100;var n=e[2]/100;if(r===0){return[n*255,n*255,n*255]}var u=[0,0,0];var o=t%1*6;var s=o%1;var a=1-s;var i=0;switch(Math.floor(o)){case 0:u[0]=1;u[1]=s;u[2]=0;break;case 1:u[0]=a;u[1]=1;u[2]=0;break;case 2:u[0]=0;u[1]=1;u[2]=s;break;case 3:u[0]=0;u[1]=a;u[2]=1;break;case 4:u[0]=s;u[1]=0;u[2]=1;break;default:u[0]=1;u[1]=0;u[2]=a}i=(1-r)*n;return[(r*u[0]+i)*255,(r*u[1]+i)*255,(r*u[2]+i)*255]};s.hcg.hsv=function(e){var t=e[1]/100;var r=e[2]/100;var n=t+r*(1-t);var u=0;if(n>0){u=t/n}return[e[0],u*100,n*100]};s.hcg.hsl=function(e){var t=e[1]/100;var r=e[2]/100;var n=r*(1-t)+.5*t;var u=0;if(n>0&&n<.5){u=t/(2*n)}else if(n>=.5&&n<1){u=t/(2*(1-n))}return[e[0],u*100,n*100]};s.hcg.hwb=function(e){var t=e[1]/100;var r=e[2]/100;var n=t+r*(1-t);return[e[0],(n-t)*100,(1-n)*100]};s.hwb.hcg=function(e){var t=e[1]/100;var r=e[2]/100;var n=1-r;var u=n-t;var o=0;if(u<1){o=(n-u)/(1-u)}return[e[0],u*100,o*100]};s.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]};s.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]};s.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]};s.gray.hsl=s.gray.hsv=function(e){return[0,0,e[0]]};s.gray.hwb=function(e){return[0,100,e[0]]};s.gray.cmyk=function(e){return[0,0,0,e[0]]};s.gray.lab=function(e){return[e[0],0,0]};s.gray.hex=function(e){var t=Math.round(e[0]/100*255)&255;var r=(t<<16)+(t<<8)+t;var n=r.toString(16).toUpperCase();return"000000".substring(n.length)+n};s.rgb.gray=function(e){var t=(e[0]+e[1]+e[2])/3;return[t/255*100]}},54:function(e,t,r){var n=r(117);var u=r(528);var o={};var s=Object.keys(n);function wrapRaw(e){var wrappedFn=function(t){if(t===undefined||t===null){return t}if(arguments.length>1){t=Array.prototype.slice.call(arguments)}return e(t)};if("conversion"in e){wrappedFn.conversion=e.conversion}return wrappedFn}function wrapRounded(e){var wrappedFn=function(t){if(t===undefined||t===null){return t}if(arguments.length>1){t=Array.prototype.slice.call(arguments)}var r=e(t);if(typeof r==="object"){for(var n=r.length,u=0;u<n;u++){r[u]=Math.round(r[u])}}return r};if("conversion"in e){wrappedFn.conversion=e.conversion}return wrappedFn}s.forEach((function(e){o[e]={};Object.defineProperty(o[e],"channels",{value:n[e].channels});Object.defineProperty(o[e],"labels",{value:n[e].labels});var t=u(e);var r=Object.keys(t);r.forEach((function(r){var n=t[r];o[e][r]=wrapRounded(n);o[e][r].raw=wrapRaw(n)}))}));e.exports=o},528:function(e,t,r){var n=r(117);function buildGraph(){var e={};var t=Object.keys(n);for(var r=t.length,u=0;u<r;u++){e[t[u]]={distance:-1,parent:null}}return e}function deriveBFS(e){var t=buildGraph();var r=[e];t[e].distance=0;while(r.length){var u=r.pop();var o=Object.keys(n[u]);for(var s=o.length,a=0;a<s;a++){var i=o[a];var l=t[i];if(l.distance===-1){l.distance=t[u].distance+1;l.parent=u;r.unshift(i)}}}return t}function link(e,t){return function(r){return t(e(r))}}function wrapConversion(e,t){var r=[t[e].parent,e];var u=n[t[e].parent][e];var o=t[e].parent;while(t[o].parent){r.unshift(t[o].parent);u=link(n[t[o].parent][o],u);o=t[o].parent}u.conversion=r;return u}e.exports=function(e){var t=deriveBFS(e);var r={};var n=Object.keys(t);for(var u=n.length,o=0;o<u;o++){var s=n[o];var a=t[s];if(a.parent===null){continue}r[s]=wrapConversion(s,t)}return r}},113:function(e,t,r){const n=r(993);const u={};for(const e of Object.keys(n)){u[n[e]]=e}const o={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};e.exports=o;for(const e of Object.keys(o)){if(!("channels"in o[e])){throw new Error("missing channels property: "+e)}if(!("labels"in o[e])){throw new Error("missing channel labels property: "+e)}if(o[e].labels.length!==o[e].channels){throw new Error("channel and label counts mismatch: "+e)}const{channels:t,labels:r}=o[e];delete o[e].channels;delete o[e].labels;Object.defineProperty(o[e],"channels",{value:t});Object.defineProperty(o[e],"labels",{value:r})}o.rgb.hsl=function(e){const t=e[0]/255;const r=e[1]/255;const n=e[2]/255;const u=Math.min(t,r,n);const o=Math.max(t,r,n);const s=o-u;let a;let i;if(o===u){a=0}else if(t===o){a=(r-n)/s}else if(r===o){a=2+(n-t)/s}else if(n===o){a=4+(t-r)/s}a=Math.min(a*60,360);if(a<0){a+=360}const l=(u+o)/2;if(o===u){i=0}else if(l<=.5){i=s/(o+u)}else{i=s/(2-o-u)}return[a,i*100,l*100]};o.rgb.hsv=function(e){let t;let r;let n;let u;let o;const s=e[0]/255;const a=e[1]/255;const i=e[2]/255;const l=Math.max(s,a,i);const c=l-Math.min(s,a,i);const diffc=function(e){return(l-e)/6/c+1/2};if(c===0){u=0;o=0}else{o=c/l;t=diffc(s);r=diffc(a);n=diffc(i);if(s===l){u=n-r}else if(a===l){u=1/3+t-n}else if(i===l){u=2/3+r-t}if(u<0){u+=1}else if(u>1){u-=1}}return[u*360,o*100,l*100]};o.rgb.hwb=function(e){const t=e[0];const r=e[1];let n=e[2];const u=o.rgb.hsl(e)[0];const s=1/255*Math.min(t,Math.min(r,n));n=1-1/255*Math.max(t,Math.max(r,n));return[u,s*100,n*100]};o.rgb.cmyk=function(e){const t=e[0]/255;const r=e[1]/255;const n=e[2]/255;const u=Math.min(1-t,1-r,1-n);const o=(1-t-u)/(1-u)||0;const s=(1-r-u)/(1-u)||0;const a=(1-n-u)/(1-u)||0;return[o*100,s*100,a*100,u*100]};function comparativeDistance(e,t){return(e[0]-t[0])**2+(e[1]-t[1])**2+(e[2]-t[2])**2}o.rgb.keyword=function(e){const t=u[e];if(t){return t}let r=Infinity;let o;for(const t of Object.keys(n)){const u=n[t];const s=comparativeDistance(e,u);if(s<r){r=s;o=t}}return o};o.keyword.rgb=function(e){return n[e]};o.rgb.xyz=function(e){let t=e[0]/255;let r=e[1]/255;let n=e[2]/255;t=t>.04045?((t+.055)/1.055)**2.4:t/12.92;r=r>.04045?((r+.055)/1.055)**2.4:r/12.92;n=n>.04045?((n+.055)/1.055)**2.4:n/12.92;const u=t*.4124+r*.3576+n*.1805;const o=t*.2126+r*.7152+n*.0722;const s=t*.0193+r*.1192+n*.9505;return[u*100,o*100,s*100]};o.rgb.lab=function(e){const t=o.rgb.xyz(e);let r=t[0];let n=t[1];let u=t[2];r/=95.047;n/=100;u/=108.883;r=r>.008856?r**(1/3):7.787*r+16/116;n=n>.008856?n**(1/3):7.787*n+16/116;u=u>.008856?u**(1/3):7.787*u+16/116;const s=116*n-16;const a=500*(r-n);const i=200*(n-u);return[s,a,i]};o.hsl.rgb=function(e){const t=e[0]/360;const r=e[1]/100;const n=e[2]/100;let u;let o;let s;if(r===0){s=n*255;return[s,s,s]}if(n<.5){u=n*(1+r)}else{u=n+r-n*r}const a=2*n-u;const i=[0,0,0];for(let e=0;e<3;e++){o=t+1/3*-(e-1);if(o<0){o++}if(o>1){o--}if(6*o<1){s=a+(u-a)*6*o}else if(2*o<1){s=u}else if(3*o<2){s=a+(u-a)*(2/3-o)*6}else{s=a}i[e]=s*255}return i};o.hsl.hsv=function(e){const t=e[0];let r=e[1]/100;let n=e[2]/100;let u=r;const o=Math.max(n,.01);n*=2;r*=n<=1?n:2-n;u*=o<=1?o:2-o;const s=(n+r)/2;const a=n===0?2*u/(o+u):2*r/(n+r);return[t,a*100,s*100]};o.hsv.rgb=function(e){const t=e[0]/60;const r=e[1]/100;let n=e[2]/100;const u=Math.floor(t)%6;const o=t-Math.floor(t);const s=255*n*(1-r);const a=255*n*(1-r*o);const i=255*n*(1-r*(1-o));n*=255;switch(u){case 0:return[n,i,s];case 1:return[a,n,s];case 2:return[s,n,i];case 3:return[s,a,n];case 4:return[i,s,n];case 5:return[n,s,a]}};o.hsv.hsl=function(e){const t=e[0];const r=e[1]/100;const n=e[2]/100;const u=Math.max(n,.01);let o;let s;s=(2-r)*n;const a=(2-r)*u;o=r*u;o/=a<=1?a:2-a;o=o||0;s/=2;return[t,o*100,s*100]};o.hwb.rgb=function(e){const t=e[0]/360;let r=e[1]/100;let n=e[2]/100;const u=r+n;let o;if(u>1){r/=u;n/=u}const s=Math.floor(6*t);const a=1-n;o=6*t-s;if((s&1)!==0){o=1-o}const i=r+o*(a-r);let l;let c;let f;switch(s){default:case 6:case 0:l=a;c=i;f=r;break;case 1:l=i;c=a;f=r;break;case 2:l=r;c=a;f=i;break;case 3:l=r;c=i;f=a;break;case 4:l=i;c=r;f=a;break;case 5:l=a;c=r;f=i;break}return[l*255,c*255,f*255]};o.cmyk.rgb=function(e){const t=e[0]/100;const r=e[1]/100;const n=e[2]/100;const u=e[3]/100;const o=1-Math.min(1,t*(1-u)+u);const s=1-Math.min(1,r*(1-u)+u);const a=1-Math.min(1,n*(1-u)+u);return[o*255,s*255,a*255]};o.xyz.rgb=function(e){const t=e[0]/100;const r=e[1]/100;const n=e[2]/100;let u;let o;let s;u=t*3.2406+r*-1.5372+n*-.4986;o=t*-.9689+r*1.8758+n*.0415;s=t*.0557+r*-.204+n*1.057;u=u>.0031308?1.055*u**(1/2.4)-.055:u*12.92;o=o>.0031308?1.055*o**(1/2.4)-.055:o*12.92;s=s>.0031308?1.055*s**(1/2.4)-.055:s*12.92;u=Math.min(Math.max(0,u),1);o=Math.min(Math.max(0,o),1);s=Math.min(Math.max(0,s),1);return[u*255,o*255,s*255]};o.xyz.lab=function(e){let t=e[0];let r=e[1];let n=e[2];t/=95.047;r/=100;n/=108.883;t=t>.008856?t**(1/3):7.787*t+16/116;r=r>.008856?r**(1/3):7.787*r+16/116;n=n>.008856?n**(1/3):7.787*n+16/116;const u=116*r-16;const o=500*(t-r);const s=200*(r-n);return[u,o,s]};o.lab.xyz=function(e){const t=e[0];const r=e[1];const n=e[2];let u;let o;let s;o=(t+16)/116;u=r/500+o;s=o-n/200;const a=o**3;const i=u**3;const l=s**3;o=a>.008856?a:(o-16/116)/7.787;u=i>.008856?i:(u-16/116)/7.787;s=l>.008856?l:(s-16/116)/7.787;u*=95.047;o*=100;s*=108.883;return[u,o,s]};o.lab.lch=function(e){const t=e[0];const r=e[1];const n=e[2];let u;const o=Math.atan2(n,r);u=o*360/2/Math.PI;if(u<0){u+=360}const s=Math.sqrt(r*r+n*n);return[t,s,u]};o.lch.lab=function(e){const t=e[0];const r=e[1];const n=e[2];const u=n/360*2*Math.PI;const o=r*Math.cos(u);const s=r*Math.sin(u);return[t,o,s]};o.rgb.ansi16=function(e,t=null){const[r,n,u]=e;let s=t===null?o.rgb.hsv(e)[2]:t;s=Math.round(s/50);if(s===0){return 30}let a=30+(Math.round(u/255)<<2|Math.round(n/255)<<1|Math.round(r/255));if(s===2){a+=60}return a};o.hsv.ansi16=function(e){return o.rgb.ansi16(o.hsv.rgb(e),e[2])};o.rgb.ansi256=function(e){const t=e[0];const r=e[1];const n=e[2];if(t===r&&r===n){if(t<8){return 16}if(t>248){return 231}return Math.round((t-8)/247*24)+232}const u=16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5);return u};o.ansi16.rgb=function(e){let t=e%10;if(t===0||t===7){if(e>50){t+=3.5}t=t/10.5*255;return[t,t,t]}const r=(~~(e>50)+1)*.5;const n=(t&1)*r*255;const u=(t>>1&1)*r*255;const o=(t>>2&1)*r*255;return[n,u,o]};o.ansi256.rgb=function(e){if(e>=232){const t=(e-232)*10+8;return[t,t,t]}e-=16;let t;const r=Math.floor(e/36)/5*255;const n=Math.floor((t=e%36)/6)/5*255;const u=t%6/5*255;return[r,n,u]};o.rgb.hex=function(e){const t=((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255);const r=t.toString(16).toUpperCase();return"000000".substring(r.length)+r};o.hex.rgb=function(e){const t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t){return[0,0,0]}let r=t[0];if(t[0].length===3){r=r.split("").map((e=>e+e)).join("")}const n=parseInt(r,16);const u=n>>16&255;const o=n>>8&255;const s=n&255;return[u,o,s]};o.rgb.hcg=function(e){const t=e[0]/255;const r=e[1]/255;const n=e[2]/255;const u=Math.max(Math.max(t,r),n);const o=Math.min(Math.min(t,r),n);const s=u-o;let a;let i;if(s<1){a=o/(1-s)}else{a=0}if(s<=0){i=0}else if(u===t){i=(r-n)/s%6}else if(u===r){i=2+(n-t)/s}else{i=4+(t-r)/s}i/=6;i%=1;return[i*360,s*100,a*100]};o.hsl.hcg=function(e){const t=e[1]/100;const r=e[2]/100;const n=r<.5?2*t*r:2*t*(1-r);let u=0;if(n<1){u=(r-.5*n)/(1-n)}return[e[0],n*100,u*100]};o.hsv.hcg=function(e){const t=e[1]/100;const r=e[2]/100;const n=t*r;let u=0;if(n<1){u=(r-n)/(1-n)}return[e[0],n*100,u*100]};o.hcg.rgb=function(e){const t=e[0]/360;const r=e[1]/100;const n=e[2]/100;if(r===0){return[n*255,n*255,n*255]}const u=[0,0,0];const o=t%1*6;const s=o%1;const a=1-s;let i=0;switch(Math.floor(o)){case 0:u[0]=1;u[1]=s;u[2]=0;break;case 1:u[0]=a;u[1]=1;u[2]=0;break;case 2:u[0]=0;u[1]=1;u[2]=s;break;case 3:u[0]=0;u[1]=a;u[2]=1;break;case 4:u[0]=s;u[1]=0;u[2]=1;break;default:u[0]=1;u[1]=0;u[2]=a}i=(1-r)*n;return[(r*u[0]+i)*255,(r*u[1]+i)*255,(r*u[2]+i)*255]};o.hcg.hsv=function(e){const t=e[1]/100;const r=e[2]/100;const n=t+r*(1-t);let u=0;if(n>0){u=t/n}return[e[0],u*100,n*100]};o.hcg.hsl=function(e){const t=e[1]/100;const r=e[2]/100;const n=r*(1-t)+.5*t;let u=0;if(n>0&&n<.5){u=t/(2*n)}else if(n>=.5&&n<1){u=t/(2*(1-n))}return[e[0],u*100,n*100]};o.hcg.hwb=function(e){const t=e[1]/100;const r=e[2]/100;const n=t+r*(1-t);return[e[0],(n-t)*100,(1-n)*100]};o.hwb.hcg=function(e){const t=e[1]/100;const r=e[2]/100;const n=1-r;const u=n-t;let o=0;if(u<1){o=(n-u)/(1-u)}return[e[0],u*100,o*100]};o.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]};o.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]};o.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]};o.gray.hsl=function(e){return[0,0,e[0]]};o.gray.hsv=o.gray.hsl;o.gray.hwb=function(e){return[0,100,e[0]]};o.gray.cmyk=function(e){return[0,0,0,e[0]]};o.gray.lab=function(e){return[e[0],0,0]};o.gray.hex=function(e){const t=Math.round(e[0]/100*255)&255;const r=(t<<16)+(t<<8)+t;const n=r.toString(16).toUpperCase();return"000000".substring(n.length)+n};o.rgb.gray=function(e){const t=(e[0]+e[1]+e[2])/3;return[t/255*100]}},226:function(e,t,r){const n=r(113);const u=r(971);const o={};const s=Object.keys(n);function wrapRaw(e){const wrappedFn=function(...t){const r=t[0];if(r===undefined||r===null){return r}if(r.length>1){t=r}return e(t)};if("conversion"in e){wrappedFn.conversion=e.conversion}return wrappedFn}function wrapRounded(e){const wrappedFn=function(...t){const r=t[0];if(r===undefined||r===null){return r}if(r.length>1){t=r}const n=e(t);if(typeof n==="object"){for(let e=n.length,t=0;t<e;t++){n[t]=Math.round(n[t])}}return n};if("conversion"in e){wrappedFn.conversion=e.conversion}return wrappedFn}s.forEach((e=>{o[e]={};Object.defineProperty(o[e],"channels",{value:n[e].channels});Object.defineProperty(o[e],"labels",{value:n[e].labels});const t=u(e);const r=Object.keys(t);r.forEach((r=>{const n=t[r];o[e][r]=wrapRounded(n);o[e][r].raw=wrapRaw(n)}))}));e.exports=o},971:function(e,t,r){const n=r(113);function buildGraph(){const e={};const t=Object.keys(n);for(let r=t.length,n=0;n<r;n++){e[t[n]]={distance:-1,parent:null}}return e}function deriveBFS(e){const t=buildGraph();const r=[e];t[e].distance=0;while(r.length){const e=r.pop();const u=Object.keys(n[e]);for(let n=u.length,o=0;o<n;o++){const n=u[o];const s=t[n];if(s.distance===-1){s.distance=t[e].distance+1;s.parent=e;r.unshift(n)}}}return t}function link(e,t){return function(r){return t(e(r))}}function wrapConversion(e,t){const r=[t[e].parent,e];let u=n[t[e].parent][e];let o=t[e].parent;while(t[o].parent){r.unshift(t[o].parent);u=link(n[t[o].parent][o],u);o=t[o].parent}u.conversion=r;return u}e.exports=function(e){const t=deriveBFS(e);const r={};const n=Object.keys(t);for(let e=n.length,u=0;u<e;u++){const e=n[u];const o=t[e];if(o.parent===null){continue}r[e]=wrapConversion(e,t)}return r}},251:function(e){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},993:function(e){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},379:function(e){"use strict";var t=/[|\\{}()[\]^$+*?.]/g;e.exports=function(e){if(typeof e!=="string"){throw new TypeError("Expected a string")}return e.replace(t,"\\$&")}},343:function(e){"use strict";e.exports=(e,t)=>{t=t||process.argv;const r=e.startsWith("-")?"":e.length===1?"-":"--";const n=t.indexOf(r+e);const u=t.indexOf("--");return n!==-1&&(u===-1?true:n<u)}},914:function(e){"use strict";e.exports=(e,t=process.argv)=>{const r=e.startsWith("-")?"":e.length===1?"-":"--";const n=t.indexOf(r+e);const u=t.indexOf("--");return n!==-1&&(u===-1||n<u)}},874:function(e,t){Object.defineProperty(t,"__esModule",{value:true});t["default"]=/((['"])(?:(?!\2|\\).|\\(?:\r\n|[\s\S]))*(\2)?|`(?:[^`\\$]|\\[\s\S]|\$(?!\{)|\$\{(?:[^{}]|\{[^}]*\}?)*\}?)*(`)?)|(\/\/.*)|(\/\*(?:[^*]|\*(?!\/))*(\*\/)?)|(\/(?!\*)(?:\[(?:(?![\]\\]).|\\.)*\]|(?![\/\]\\]).|\\.)+\/(?:(?!\s*(?:\b|[\u0080-\uFFFF$\\'"~({]|[+\-!](?!=)|\.?\d))|[gmiyus]{1,6}\b(?![\u0080-\uFFFF$\\]|\s*(?:[+\-*%&|^<>!=?({]|\/(?![\/*])))))|(0[xX][\da-fA-F]+|0[oO][0-7]+|0[bB][01]+|(?:\d*\.\d+|\d+\.?)(?:[eE][+-]?\d+)?)|((?!\d)(?:(?!\s)[$\w\u0080-\uFFFF]|\\u[\da-fA-F]{4}|\\u\{[\da-fA-F]+\})+)|(--|\+\+|&&|\|\||=>|\.{3}|(?:[+\-\/%&|^]|\*{1,2}|<{1,2}|>{1,3}|!=?|={1,2})=?|[?~.,:;[\](){}])|(\s+)|(^$|[\s\S])/g;t.matchToToken=function(e){var t={type:"invalid",value:e[0],closed:undefined};if(e[1])t.type="string",t.closed=!!(e[3]||e[4]);else if(e[5])t.type="comment";else if(e[6])t.type="comment",t.closed=!!e[7];else if(e[8])t.type="regex";else if(e[9])t.type="number";else if(e[10])t.type="name";else if(e[11])t.type="punctuator";else if(e[12])t.type="whitespace";return t}},220:function(e,t,r){"use strict";const n=r(37);const u=r(343);const o=process.env;let s;if(u("no-color")||u("no-colors")||u("color=false")){s=false}else if(u("color")||u("colors")||u("color=true")||u("color=always")){s=true}if("FORCE_COLOR"in o){s=o.FORCE_COLOR.length===0||parseInt(o.FORCE_COLOR,10)!==0}function translateLevel(e){if(e===0){return false}return{level:e,hasBasic:true,has256:e>=2,has16m:e>=3}}function supportsColor(e){if(s===false){return 0}if(u("color=16m")||u("color=full")||u("color=truecolor")){return 3}if(u("color=256")){return 2}if(e&&!e.isTTY&&s!==true){return 0}const t=s?1:0;if(process.platform==="win32"){const e=n.release().split(".");if(Number(process.versions.node.split(".")[0])>=8&&Number(e[0])>=10&&Number(e[2])>=10586){return Number(e[2])>=14931?3:2}return 1}if("CI"in o){if(["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some((e=>e in o))||o.CI_NAME==="codeship"){return 1}return t}if("TEAMCITY_VERSION"in o){return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(o.TEAMCITY_VERSION)?1:0}if(o.COLORTERM==="truecolor"){return 3}if("TERM_PROGRAM"in o){const e=parseInt((o.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(o.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}if(/-256(color)?$/i.test(o.TERM)){return 2}if(/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(o.TERM)){return 1}if("COLORTERM"in o){return 1}if(o.TERM==="dumb"){return t}return t}function getSupportLevel(e){const t=supportsColor(e);return translateLevel(t)}e.exports={supportsColor:getSupportLevel,stdout:getSupportLevel(process.stdout),stderr:getSupportLevel(process.stderr)}},793:function(e,t,r){"use strict";const n=r(37);const u=r(224);const o=r(914);const{env:s}=process;let a;if(o("no-color")||o("no-colors")||o("color=false")||o("color=never")){a=0}else if(o("color")||o("colors")||o("color=true")||o("color=always")){a=1}if("FORCE_COLOR"in s){if(s.FORCE_COLOR==="true"){a=1}else if(s.FORCE_COLOR==="false"){a=0}else{a=s.FORCE_COLOR.length===0?1:Math.min(parseInt(s.FORCE_COLOR,10),3)}}function translateLevel(e){if(e===0){return false}return{level:e,hasBasic:true,has256:e>=2,has16m:e>=3}}function supportsColor(e,t){if(a===0){return 0}if(o("color=16m")||o("color=full")||o("color=truecolor")){return 3}if(o("color=256")){return 2}if(e&&!t&&a===undefined){return 0}const r=a||0;if(s.TERM==="dumb"){return r}if(process.platform==="win32"){const e=n.release().split(".");if(Number(e[0])>=10&&Number(e[2])>=10586){return Number(e[2])>=14931?3:2}return 1}if("CI"in s){if(["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((e=>e in s))||s.CI_NAME==="codeship"){return 1}return r}if("TEAMCITY_VERSION"in s){return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(s.TEAMCITY_VERSION)?1:0}if(s.COLORTERM==="truecolor"){return 3}if("TERM_PROGRAM"in s){const e=parseInt((s.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(s.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}if(/-256(color)?$/i.test(s.TERM)){return 2}if(/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(s.TERM)){return 1}if("COLORTERM"in s){return 1}return r}function getSupportLevel(e){const t=supportsColor(e,e&&e.isTTY);return translateLevel(t)}e.exports={supportsColor:getSupportLevel,stdout:translateLevel(supportsColor(true,u.isatty(1))),stderr:translateLevel(supportsColor(true,u.isatty(2)))}},259:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.launchEditor=void 0;const u=n(r(802));const o=n(r(81));const s=n(r(147));const a=n(r(37));const i=n(r(17));const l=n(r(938));function isTerminalEditor(e){switch(e){case"vi":case"vim":case"emacs":case"nano":{return true}default:{}}return false}const c={"/Applications/Atom.app/Contents/MacOS/Atom":"atom","/Applications/Atom Beta.app/Contents/MacOS/Atom Beta":"/Applications/Atom Beta.app/Contents/MacOS/Atom Beta","/Applications/Brackets.app/Contents/MacOS/Brackets":"brackets","/Applications/Sublime Text.app/Contents/MacOS/Sublime Text":"/Applications/Sublime Text.app/Contents/SharedSupport/bin/subl","/Applications/Sublime Text Dev.app/Contents/MacOS/Sublime Text":"/Applications/Sublime Text Dev.app/Contents/SharedSupport/bin/subl","/Applications/Sublime Text 2.app/Contents/MacOS/Sublime Text 2":"/Applications/Sublime Text 2.app/Contents/SharedSupport/bin/subl","/Applications/Visual Studio Code.app/Contents/MacOS/Electron":"/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code","/Applications/Visual Studio Code - Insiders.app/Contents/MacOS/Electron":"/Applications/Visual Studio Code - Insiders.app/Contents/Resources/app/bin/code","/Applications/VSCodium.app/Contents/MacOS/Electron":"/Applications/VSCodium.app/Contents/Resources/app/bin/code","/Applications/AppCode.app/Contents/MacOS/appcode":"/Applications/AppCode.app/Contents/MacOS/appcode","/Applications/CLion.app/Contents/MacOS/clion":"/Applications/CLion.app/Contents/MacOS/clion","/Applications/IntelliJ IDEA.app/Contents/MacOS/idea":"/Applications/IntelliJ IDEA.app/Contents/MacOS/idea","/Applications/PhpStorm.app/Contents/MacOS/phpstorm":"/Applications/PhpStorm.app/Contents/MacOS/phpstorm","/Applications/PyCharm.app/Contents/MacOS/pycharm":"/Applications/PyCharm.app/Contents/MacOS/pycharm","/Applications/PyCharm CE.app/Contents/MacOS/pycharm":"/Applications/PyCharm CE.app/Contents/MacOS/pycharm","/Applications/RubyMine.app/Contents/MacOS/rubymine":"/Applications/RubyMine.app/Contents/MacOS/rubymine","/Applications/WebStorm.app/Contents/MacOS/webstorm":"/Applications/WebStorm.app/Contents/MacOS/webstorm","/Applications/MacVim.app/Contents/MacOS/MacVim":"mvim","/Applications/GoLand.app/Contents/MacOS/goland":"/Applications/GoLand.app/Contents/MacOS/goland","/Applications/Rider.app/Contents/MacOS/rider":"/Applications/Rider.app/Contents/MacOS/rider"};const f={atom:"atom",Brackets:"brackets",code:"code","code-insiders":"code-insiders",vscodium:"vscodium",emacs:"emacs",gvim:"gvim","idea.sh":"idea","phpstorm.sh":"phpstorm","pycharm.sh":"pycharm","rubymine.sh":"rubymine",sublime_text:"sublime_text",vim:"vim","webstorm.sh":"webstorm","goland.sh":"goland","rider.sh":"rider"};const p=["Brackets.exe","Code.exe","Code - Insiders.exe","VSCodium.exe","atom.exe","sublime_text.exe","notepad++.exe","clion.exe","clion64.exe","idea.exe","idea64.exe","phpstorm.exe","phpstorm64.exe","pycharm.exe","pycharm64.exe","rubymine.exe","rubymine64.exe","webstorm.exe","webstorm64.exe","goland.exe","goland64.exe","rider.exe","rider64.exe"];const h=/^([A-Za-z]:[/\\])?(?:[\x2D-9A-Z\\_a-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEF\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7B9\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDF00-\uDF1C\uDF27\uDF30-\uDF45]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF1A]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDE9D\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFF1]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D])+$/;function getArgumentsForLineNumber(e,t,r,n){const u=i.default.basename(e).replace(/\.(exe|cmd|bat)$/i,"");switch(u){case"atom":case"Atom":case"Atom Beta":case"subl":case"sublime":case"sublime_text":{return[t+":"+r+":"+n]}case"wstorm":case"charm":{return[t+":"+r]}case"notepad++":{return["-n"+r,"-c"+n,t]}case"vim":case"mvim":case"joe":case"gvim":{return["+"+r,t]}case"emacs":case"emacsclient":{return["+"+r+":"+n,t]}case"rmate":case"mate":case"mine":{return["--line",r.toString(),t]}case"code":case"Code":case"code-insiders":case"Code - Insiders":case"vscodium":case"VSCodium":{return["-g",t+":"+r+":"+n]}case"appcode":case"clion":case"clion64":case"idea":case"idea64":case"phpstorm":case"phpstorm64":case"pycharm":case"pycharm64":case"rubymine":case"rubymine64":case"webstorm":case"webstorm64":case"goland":case"goland64":case"rider":case"rider64":{return["--line",r.toString(),t]}default:{return[t]}}}function guessEditor(){if(process.env.REACT_EDITOR){return l.default.parse(process.env.REACT_EDITOR)}try{if(process.platform==="darwin"){const e=o.default.execSync("ps x").toString();const t=Object.keys(c);for(let r=0;r<t.length;r++){const n=t[r];if(e.includes(n)){return[c[n]]}}}else if(process.platform==="win32"){const e=o.default.execSync('wmic process where "executablepath is not null" get executablepath').toString();const t=e.split("\r\n");for(let e=0;e<t.length;e++){const r=t[e].trim();const n=i.default.basename(r);if(p.includes(n)){return[r]}}}else if(process.platform==="linux"){const e=o.default.execSync("ps x --no-heading -o comm --sort=comm").toString();const t=Object.keys(f);for(let r=0;r<t.length;r++){const n=t[r];if(e.includes(n)){return[f[n]]}}}}catch(e){}if(process.env.VISUAL){return[process.env.VISUAL]}else if(process.env.EDITOR){return[process.env.EDITOR]}return[]}function printInstructions(e,t){console.log();console.log(u.default.red("Could not open "+i.default.basename(e)+" in the editor."));if(t){if(t[t.length-1]!=="."){t+="."}console.log(u.default.red("The editor process exited with an error: "+t))}console.log();console.log("To set up the editor integration, add something like "+u.default.cyan("REACT_EDITOR=atom")+" to the "+u.default.green(".env.local")+" file in your project folder "+"and restart the development server.");console.log()}function launchEditor(e,t,r){if(!s.default.existsSync(e)){return}if(!(Number.isInteger(t)&&t>0)){return}if(!(Number.isInteger(r)&&r>0)){r=1}let[n,...c]=guessEditor();if(!n){printInstructions(e,null);return}if(n.toLowerCase()==="none"){return}if(process.platform==="linux"&&e.startsWith("/mnt/")&&/Microsoft/i.test(a.default.release())){e=i.default.relative("",e)}if(process.platform==="win32"&&!h.test(e.trim())){console.log();console.log(u.default.red("Could not open "+i.default.basename(e)+" in the editor."));console.log();console.log("When running on Windows, file names are checked against an access list "+"to protect against remote code execution attacks. File names may "+"consist only of alphanumeric characters (all languages), periods, "+"dashes, slashes, and underscores.");console.log();return}if(t){c=c.concat(getArgumentsForLineNumber(n,e,t,r))}else{c.push(e)}let f=undefined;if(process.platform==="win32"){f=o.default.spawn("cmd.exe",["/C",n].concat(c),{stdio:"inherit",detached:true})}else if(isTerminalEditor(n)){if(process.platform==="darwin"){f=o.default.spawn("osascript",["-e",`tell application "Terminal" to do script "${l.default.quote([n,...c])}"`],{stdio:"ignore"})}else{printInstructions(e,"Terminal editors can only be used on macOS.")}}else{f=o.default.spawn(n,c,{stdio:"inherit"})}if(f){f.on("exit",(function(t){if(t){printInstructions(e,"(code "+t+")")}}));f.on("error",(function(t){printInstructions(e,t.message)}))}}t.launchEditor=launchEditor},941:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;var u=Object.getOwnPropertyDescriptor(t,r);if(!u||("get"in u?!t.__esModule:u.writable||u.configurable)){u={enumerable:true,get:function(){return t[r]}}}Object.defineProperty(e,n,u)}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var u=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:true,value:t})}:function(e,t){e["default"]=t});var o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)if(r!=="default"&&Object.prototype.hasOwnProperty.call(e,r))n(t,e,r);u(t,e);return t};var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:true});t.getOverlayMiddleware=t.createOriginalStackFrame=void 0;const a=o(r(292));const i=s(r(310));const l=r(430);const c=r(259);const f=new Map;async function batchedTraceSource(e,t){const r=t.file?decodeURIComponent(t.file):undefined;if(!r){return}const n=await e.traceSource(t);if(!n){return}let u;if(!n.file.includes("node_modules")){let t=f.get(n.file);if(!t){t=e.getSourceForAsset(n.file);f.set(n.file,t);setTimeout((()=>{f.delete(n.file)}),100)}u=await t}return{frame:{file:n.file,lineNumber:n.line,column:n.column,methodName:n.methodName??t.methodName??"<unknown>",arguments:[]},source:u??null}}async function createOriginalStackFrame(e,t){const r=await batchedTraceSource(e,t);if(!r){return null}return{originalStackFrame:r.frame,originalCodeFrame:r.source===null||r.frame.file.includes("node_modules")?null:(0,l.codeFrameColumns)(r.source,{start:{line:r.frame.lineNumber,column:r.frame.column??0}},{forceColor:true})}}t.createOriginalStackFrame=createOriginalStackFrame;function stackFrameFromQuery(e){return{file:e.file,methodName:e.methodName,line:typeof e.lineNumber==="string"?parseInt(e.lineNumber,10):0,column:typeof e.column==="string"?parseInt(e.column,10):null,isServer:e.isServer==="true"}}function getOverlayMiddleware(e){return async function(t,r){const{pathname:n,query:u}=i.default.parse(t.url,true);if(n==="/__nextjs_original-stack-frame"){const t=stackFrameFromQuery(u);let n;try{n=await createOriginalStackFrame(e,t)}catch(e){r.statusCode=500;r.write(e.message);r.end();return}if(n===null){r.statusCode=404;r.write("Unable to resolve sourcemap");r.end();return}r.statusCode=200;r.setHeader("Content-Type","application/json");r.write(Buffer.from(JSON.stringify(n)));r.end();return}else if(n==="/__nextjs_launch-editor"){const e=stackFrameFromQuery(u);const t=e.file?.toString();if(t===undefined){r.statusCode=400;r.write("Bad Request");r.end();return}const n=await a.default.access(t,a.constants.F_OK).then((()=>true),(()=>false));if(!n){r.statusCode=204;r.write("No Content");r.end();return}try{(0,c.launchEditor)(t,e.line,e.column??1)}catch(e){console.log("Failed to launch editor:",e);r.statusCode=500;r.write("Internal Server Error");r.end();return}r.statusCode=204;r.end()}}}t.getOverlayMiddleware=getOverlayMiddleware},81:function(e){"use strict";e.exports=require("child_process")},147:function(e){"use strict";e.exports=require("fs")},292:function(e){"use strict";e.exports=require("fs/promises")},938:function(e){"use strict";e.exports=require("next/dist/compiled/shell-quote")},37:function(e){"use strict";e.exports=require("os")},17:function(e){"use strict";e.exports=require("path")},224:function(e){"use strict";e.exports=require("tty")},310:function(e){"use strict";e.exports=require("url")},430:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.codeFrameColumns=codeFrameColumns;t["default"]=_default;var n=r(448);let u=false;function getDefs(e){return{gutter:e.grey,marker:e.red.bold,message:e.red.bold}}const o=/\r\n|[\n\r\u2028\u2029]/;function getMarkerLines(e,t,r){const n=Object.assign({column:0,line:-1},e.start);const u=Object.assign({},n,e.end);const{linesAbove:o=2,linesBelow:s=3}=r||{};const a=n.line;const i=n.column;const l=u.line;const c=u.column;let f=Math.max(a-(o+1),0);let p=Math.min(t.length,l+s);if(a===-1){f=0}if(l===-1){p=t.length}const h=l-a;const d={};if(h){for(let e=0;e<=h;e++){const r=e+a;if(!i){d[r]=true}else if(e===0){const e=t[r-1].length;d[r]=[i,e-i+1]}else if(e===h){d[r]=[0,c]}else{const n=t[r-e].length;d[r]=[0,n]}}}else{if(i===c){if(i){d[a]=[i,0]}else{d[a]=true}}else{d[a]=[i,c-i]}}return{start:f,end:p,markerLines:d}}function codeFrameColumns(e,t,r={}){const u=(r.highlightCode||r.forceColor)&&(0,n.shouldHighlight)(r);const s=(0,n.getChalk)(r);const a=getDefs(s);const maybeHighlight=(e,t)=>u?e(t):t;const i=e.split(o);const{start:l,end:c,markerLines:f}=getMarkerLines(t,i,r);const p=t.start&&typeof t.start.column==="number";const h=String(c).length;const d=u?(0,n.default)(e,r):e;let D=d.split(o,c).slice(l,c).map(((e,t)=>{const n=l+1+t;const u=` ${n}`.slice(-h);const o=` ${u} |`;const s=f[n];const i=!f[n+1];if(s){let t="";if(Array.isArray(s)){const n=e.slice(0,Math.max(s[0]-1,0)).replace(/[^\t]/g," ");const u=s[1]||1;t=["\n ",maybeHighlight(a.gutter,o.replace(/\d/g," "))," ",n,maybeHighlight(a.marker,"^").repeat(u)].join("");if(i&&r.message){t+=" "+maybeHighlight(a.message,r.message)}}return[maybeHighlight(a.marker,">"),maybeHighlight(a.gutter,o),e.length>0?` ${e}`:"",t].join("")}else{return` ${maybeHighlight(a.gutter,o)}${e.length>0?` ${e}`:""}`}})).join("\n");if(r.message&&!p){D=`${" ".repeat(h+1)}${r.message}\n${D}`}if(u){return s.reset(D)}else{return D}}function _default(e,t,r,n={}){if(!u){u=true;const e="Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.";if(process.emitWarning){process.emitWarning(e,"DeprecationWarning")}else{const t=new Error(e);t.name="DeprecationWarning";console.warn(new Error(e))}}r=Math.max(r,0);const o={start:{column:r,line:t}};return codeFrameColumns(e,o,n)}},387:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.isIdentifierChar=isIdentifierChar;t.isIdentifierName=isIdentifierName;t.isIdentifierStart=isIdentifierStart;let r="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟊꟐꟑꟓꟕ-ꟙꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ";let n="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࢘-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ೳഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-໎໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‌‍‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯・꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿･";const u=new RegExp("["+r+"]");const o=new RegExp("["+r+n+"]");r=n=null;const s=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,4026,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,757,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191];const a=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,81,2,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,9,5351,0,7,14,13835,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,983,6,110,6,6,9,4759,9,787719,239];function isInAstralSet(e,t){let r=65536;for(let n=0,u=t.length;n<u;n+=2){r+=t[n];if(r>e)return false;r+=t[n+1];if(r>=e)return true}return false}function isIdentifierStart(e){if(e<65)return e===36;if(e<=90)return true;if(e<97)return e===95;if(e<=122)return true;if(e<=65535){return e>=170&&u.test(String.fromCharCode(e))}return isInAstralSet(e,s)}function isIdentifierChar(e){if(e<48)return e===36;if(e<58)return true;if(e<65)return false;if(e<=90)return true;if(e<97)return e===95;if(e<=122)return true;if(e<=65535){return e>=170&&o.test(String.fromCharCode(e))}return isInAstralSet(e,s)||isInAstralSet(e,a)}function isIdentifierName(e){let t=true;for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);if((n&64512)===55296&&r+1<e.length){const t=e.charCodeAt(++r);if((t&64512)===56320){n=65536+((n&1023)<<10)+(t&1023)}}if(t){t=false;if(!isIdentifierStart(n)){return false}}else if(!isIdentifierChar(n)){return false}}return!t}},975:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"isIdentifierChar",{enumerable:true,get:function(){return n.isIdentifierChar}});Object.defineProperty(t,"isIdentifierName",{enumerable:true,get:function(){return n.isIdentifierName}});Object.defineProperty(t,"isIdentifierStart",{enumerable:true,get:function(){return n.isIdentifierStart}});Object.defineProperty(t,"isKeyword",{enumerable:true,get:function(){return u.isKeyword}});Object.defineProperty(t,"isReservedWord",{enumerable:true,get:function(){return u.isReservedWord}});Object.defineProperty(t,"isStrictBindOnlyReservedWord",{enumerable:true,get:function(){return u.isStrictBindOnlyReservedWord}});Object.defineProperty(t,"isStrictBindReservedWord",{enumerable:true,get:function(){return u.isStrictBindReservedWord}});Object.defineProperty(t,"isStrictReservedWord",{enumerable:true,get:function(){return u.isStrictReservedWord}});var n=r(387);var u=r(348)},348:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.isKeyword=isKeyword;t.isReservedWord=isReservedWord;t.isStrictBindOnlyReservedWord=isStrictBindOnlyReservedWord;t.isStrictBindReservedWord=isStrictBindReservedWord;t.isStrictReservedWord=isStrictReservedWord;const r={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]};const n=new Set(r.keyword);const u=new Set(r.strict);const o=new Set(r.strictBind);function isReservedWord(e,t){return t&&e==="await"||e==="enum"}function isStrictReservedWord(e,t){return isReservedWord(e,t)||u.has(e)}function isStrictBindOnlyReservedWord(e){return o.has(e)}function isStrictBindReservedWord(e,t){return isStrictReservedWord(e,t)||isStrictBindOnlyReservedWord(e)}function isKeyword(e){return n.has(e)}},448:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t["default"]=highlight;t.shouldHighlight=shouldHighlight;var n=r(874);var u=r(975);var o=_interopRequireWildcard(r(148),true);function _getRequireWildcardCache(e){if(typeof WeakMap!=="function")return null;var t=new WeakMap;var r=new WeakMap;return(_getRequireWildcardCache=function(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule){return e}if(e===null||typeof e!=="object"&&typeof e!=="function"){return{default:e}}var r=_getRequireWildcardCache(t);if(r&&r.has(e)){return r.get(e)}var n={};var u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e){if(o!=="default"&&Object.prototype.hasOwnProperty.call(e,o)){var s=u?Object.getOwnPropertyDescriptor(e,o):null;if(s&&(s.get||s.set)){Object.defineProperty(n,o,s)}else{n[o]=e[o]}}}n.default=e;if(r){r.set(e,n)}return n}const s=new Set(["as","async","from","get","of","set"]);function getDefs(e){return{keyword:e.cyan,capitalized:e.yellow,jsxIdentifier:e.yellow,punctuator:e.yellow,number:e.magenta,string:e.green,regex:e.magenta,comment:e.grey,invalid:e.white.bgRed.bold}}const a=/\r\n|[\n\r\u2028\u2029]/;const i=/^[()[\]{}]$/;let l;{const e=/^[a-z][\w-]*$/i;const getTokenType=function(t,r,n){if(t.type==="name"){if((0,u.isKeyword)(t.value)||(0,u.isStrictReservedWord)(t.value,true)||s.has(t.value)){return"keyword"}if(e.test(t.value)&&(n[r-1]==="<"||n.slice(r-2,r)=="</")){return"jsxIdentifier"}if(t.value[0]!==t.value[0].toLowerCase()){return"capitalized"}}if(t.type==="punctuator"&&i.test(t.value)){return"bracket"}if(t.type==="invalid"&&(t.value==="@"||t.value==="#")){return"punctuator"}return t.type};l=function*(e){let t;while(t=n.default.exec(e)){const r=n.matchToToken(t);yield{type:getTokenType(r,t.index,e),value:r.value}}}}function highlightTokens(e,t){let r="";for(const{type:n,value:u}of l(t)){const t=e[n];if(t){r+=u.split(a).map((e=>t(e))).join("\n")}else{r+=u}}return r}function shouldHighlight(e){return o.default.level>0||e.forceColor}let c=undefined;function getChalk(e){if(e){var t;(t=c)!=null?t:c=new o.default.constructor({enabled:true,level:1});return c}return o.default}{t.getChalk=e=>getChalk(e.forceColor)}function highlight(e,t={}){if(e!==""&&shouldHighlight(t)){const r=getDefs(getChalk(t.forceColor));return highlightTokens(r,e)}else{return e}}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var u=t[r]={id:r,loaded:false,exports:{}};var o=true;try{e[r].call(u.exports,u,u.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}u.loaded=true;return u.exports}!function(){__nccwpck_require__.nmd=function(e){e.paths=[];if(!e.children)e.children=[];return e}}();if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r=__nccwpck_require__(941);module.exports=r})();