import { Link } from 'react-router-dom';
import { ChevronDown, Menu, X } from 'lucide-react';
import { useState } from 'react';

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="bg-crypto-dark border-b border-crypto-dark-lighter">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <img
              src="https://cdn.builder.io/api/v1/image/assets%2F700ab04c03304effad4d5e385f1a769d%2F9eb2856d35f44f7bb1df91032deacafe?format=webp&width=800"
              alt="Praith Logo"
              className="h-8 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-white hover:text-neon-green transition-colors">
              Home
            </Link>
            <Link to="/product" className="text-white hover:text-neon-green transition-colors">
              Product Page
            </Link>
            <Link to="/contact" className="text-white hover:text-neon-green transition-colors">
              Contact Us
            </Link>
          </nav>

          {/* Desktop Right Side Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <button className="bg-neon-green text-crypto-dark px-6 py-2 rounded-lg font-semibold hover:bg-neon-green-dark transition-colors">
              Get Started
            </button>

            <div className="flex items-center space-x-1 text-white">
              <span>ENG</span>
              <ChevronDown className="w-4 h-4" />
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-white"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t border-crypto-dark-lighter pt-4">
            <div className="flex flex-col space-y-4">
              <Link
                to="/"
                className="text-white hover:text-neon-green transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                to="/product"
                className="text-white hover:text-neon-green transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Product Page
              </Link>
              <Link
                to="/contact"
                className="text-white hover:text-neon-green transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Contact Us
              </Link>
              <button className="bg-neon-green text-crypto-dark px-6 py-2 rounded-lg font-semibold hover:bg-neon-green-dark transition-colors w-fit">
                Get Started
              </button>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
}
