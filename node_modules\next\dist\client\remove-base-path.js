"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "removeBasePath", {
    enumerable: true,
    get: function() {
        return removeBasePath;
    }
});
const _hasbasepath = require("./has-base-path");
const basePath = process.env.__NEXT_ROUTER_BASEPATH || "";
function removeBasePath(path) {
    if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {
        if (!(0, _hasbasepath.hasBasePath)(path)) {
            return path;
        }
    }
    // Can't trim the basePath if it has zero length!
    if (basePath.length === 0) return path;
    path = path.slice(basePath.length);
    if (!path.startsWith("/")) path = "/" + path;
    return path;
}

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=remove-base-path.js.map