{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/auto-implement-methods.ts"], "names": ["autoImplementMethods", "AUTOMATIC_ROUTE_METHODS", "handlers", "methods", "HTTP_METHODS", "reduce", "acc", "method", "handleMethodNotAllowedResponse", "implemented", "Set", "filter", "missing", "has", "GET", "HEAD", "add", "allow", "push", "headers", "Allow", "sort", "join", "OPTIONS", "Response", "status", "Error"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;sBAL+B;kCACA;AAE/C,MAAMC,0BAA0B;IAAC;IAAQ;CAAU;AAE5C,SAASD,qBACdE,QAA0B;IAE1B,0EAA0E;IAC1E,mEAAmE;IACnE,MAAMC,UAAkDC,kBAAY,CAACC,MAAM,CACzE,CAACC,KAAKC,SAAY,CAAA;YAChB,GAAGD,GAAG;YACN,wEAAwE;YACxE,gCAAgC;YAChC,CAACC,OAAO,EAAEL,QAAQ,CAACK,OAAO,IAAIC,gDAA8B;QAC9D,CAAA,GACA,CAAC;IAGH,4EAA4E;IAC5E,sCAAsC;IACtC,MAAMC,cAAc,IAAIC,IAAIN,kBAAY,CAACO,MAAM,CAAC,CAACJ,SAAWL,QAAQ,CAACK,OAAO;IAC5E,MAAMK,UAAUX,wBAAwBU,MAAM,CAC5C,CAACJ,SAAW,CAACE,YAAYI,GAAG,CAACN;IAG/B,2EAA2E;IAC3E,KAAK,MAAMA,UAAUK,QAAS;QAC5B,iEAAiE;QACjE,oEAAoE;QACpE,WAAW;QACX,IAAIL,WAAW,QAAQ;YACrB,gEAAgE;YAChE,cAAc;YACd,IAAI,CAACL,SAASY,GAAG,EAAE;YAEnB,uDAAuD;YACvDX,QAAQY,IAAI,GAAGb,SAASY,GAAG;YAE3B,0BAA0B;YAC1BL,YAAYO,GAAG,CAAC;YAEhB;QACF;QAEA,gDAAgD;QAChD,IAAIT,WAAW,WAAW;YACxB,wEAAwE;YAExE,oEAAoE;YACpE,MAAMU,QAAuB;gBAAC;mBAAcR;aAAY;YAExD,yEAAyE;YACzE,8CAA8C;YAC9C,IAAI,CAACA,YAAYI,GAAG,CAAC,WAAWJ,YAAYI,GAAG,CAAC,QAAQ;gBACtDI,MAAMC,IAAI,CAAC;YACb;YAEA,wEAAwE;YACxE,oDAAoD;YACpD,MAAMC,UAAU;gBAAEC,OAAOH,MAAMI,IAAI,GAAGC,IAAI,CAAC;YAAM;YAEjD,oEAAoE;YACpE,kBAAkB;YAClBnB,QAAQoB,OAAO,GAAG,IAAM,IAAIC,SAAS,MAAM;oBAAEC,QAAQ;oBAAKN;gBAAQ;YAElE,mCAAmC;YACnCV,YAAYO,GAAG,CAAC;YAEhB;QACF;QAEA,MAAM,IAAIU,MACR,CAAC,0EAA0E,EAAEnB,OAAO,CAAC;IAEzF;IAEA,OAAOJ;AACT"}