"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _interop_require_wildcard = require("@swc/helpers/_/_interop_require_wildcard");
const _react = /*#__PURE__*/ _interop_require_wildcard._(require("react"));
const _erroroverlayreducer = require("./error-overlay-reducer");
const _ShadowPortal = require("./components/ShadowPortal");
const _BuildError = require("./container/BuildError");
const _Errors = require("./container/Errors");
const _RootLayoutError = require("./container/RootLayoutError");
const _parseStack = require("./helpers/parseStack");
const _Base = require("./styles/Base");
const _ComponentStyles = require("./styles/ComponentStyles");
const _CssReset = require("./styles/CssReset");
class ReactDevOverlay extends _react.PureComponent {
    static getDerivedStateFromError(error) {
        const e = error;
        const event = {
            type: _erroroverlayreducer.ACTION_UNHANDLED_ERROR,
            reason: error,
            frames: (0, _parseStack.parseStack)(e.stack)
        };
        const errorEvent = {
            id: 0,
            event
        };
        return {
            reactError: errorEvent
        };
    }
    componentDidCatch(componentErr) {
        this.props.onReactError(componentErr);
    }
    render() {
        const { state, children } = this.props;
        const { reactError } = this.state;
        const hasBuildError = state.buildError != null;
        const hasRuntimeErrors = Boolean(state.errors.length);
        const rootLayoutMissingTagsError = state.rootLayoutMissingTagsError;
        const isMounted = hasBuildError || hasRuntimeErrors || reactError || rootLayoutMissingTagsError;
        return /*#__PURE__*/ _react.createElement(_react.Fragment, null, reactError ? /*#__PURE__*/ _react.createElement("html", null, /*#__PURE__*/ _react.createElement("head", null), /*#__PURE__*/ _react.createElement("body", null)) : children, isMounted ? /*#__PURE__*/ _react.createElement(_ShadowPortal.ShadowPortal, null, /*#__PURE__*/ _react.createElement(_CssReset.CssReset, null), /*#__PURE__*/ _react.createElement(_Base.Base, null), /*#__PURE__*/ _react.createElement(_ComponentStyles.ComponentStyles, null), rootLayoutMissingTagsError ? /*#__PURE__*/ _react.createElement(_RootLayoutError.RootLayoutError, {
            missingTags: rootLayoutMissingTagsError.missingTags
        }) : hasBuildError ? /*#__PURE__*/ _react.createElement(_BuildError.BuildError, {
            message: state.buildError,
            versionInfo: state.versionInfo
        }) : reactError ? /*#__PURE__*/ _react.createElement(_Errors.Errors, {
            versionInfo: state.versionInfo,
            initialDisplayState: "fullscreen",
            errors: [
                reactError
            ]
        }) : hasRuntimeErrors ? /*#__PURE__*/ _react.createElement(_Errors.Errors, {
            initialDisplayState: "minimized",
            errors: state.errors,
            versionInfo: state.versionInfo
        }) : undefined) : undefined);
    }
    constructor(...args){
        super(...args);
        this.state = {
            reactError: null
        };
    }
}
const _default = ReactDevOverlay;

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=ReactDevOverlay.js.map