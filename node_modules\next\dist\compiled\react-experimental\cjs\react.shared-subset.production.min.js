/**
 * @license React
 * react.shared-subset.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var l=Object.assign,n={current:null};function p(){return new Map}
if("function"===typeof fetch){var q=fetch,r=function(a,b){var c=n.current;if(!c||b&&b.signal&&b.signal!==c.getCacheSignal())return q(a,b);if("string"!==typeof a||b){var d="string"===typeof a||a instanceof URL?new Request(a,b):a;if("GET"!==d.method&&"HEAD"!==d.method||d.keepalive)return q(a,b);var e=JSON.stringify([d.method,Array.from(d.headers.entries()),d.mode,d.redirect,d.credentials,d.referrer,d.referrerPolicy,d.integrity]);d=d.url}else e='["GET",[],null,"follow",null,null,null,null]',d=a;var f=
c.getCacheForType(p);c=f.get(d);if(void 0===c)a=q(a,b),f.set(d,[e,a]);else{d=0;for(f=c.length;d<f;d+=2){var h=c[d+1];if(c[d]===e)return a=h,a.then(function(g){return g.clone()})}a=q(a,b);c.push(e,a)}return a.then(function(g){return g.clone()})};l(r,q);try{fetch=r}catch(a){try{globalThis.fetch=r}catch(b){console.warn("React was unable to patch the fetch() function in this environment. Suspensey APIs might not work correctly as a result.")}}}
var t={current:null},u={current:null},v={},w={ReactCurrentDispatcher:t,ReactCurrentOwner:u,ContextRegistry:v},x=new WeakMap,y=new Map,z=new Set,A=new Set,B={ReactCurrentCache:n,TaintRegistryObjects:x,TaintRegistryValues:y,TaintRegistryByteLengths:z,TaintRegistryPendingRequests:A};
function C(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var D=Object.getPrototypeOf,E=B.TaintRegistryObjects,F=B.TaintRegistryValues,G=B.TaintRegistryByteLengths,aa=B.TaintRegistryPendingRequests,ba=D(Uint32Array.prototype).constructor;
function ca(a){var b=F.get(a);void 0!==b&&(aa.forEach(function(c){c.push(a);b.count++}),1===b.count?F.delete(a):b.count--)}
var H="function"===typeof FinalizationRegistry?new FinalizationRegistry(ca):null,I=Symbol.for("react.element"),da=Symbol.for("react.portal"),ea=Symbol.for("react.fragment"),fa=Symbol.for("react.strict_mode"),ha=Symbol.for("react.profiler"),ia=Symbol.for("react.provider"),ja=Symbol.for("react.server_context"),ka=Symbol.for("react.forward_ref"),la=Symbol.for("react.suspense"),ma=Symbol.for("react.suspense_list"),na=Symbol.for("react.memo"),oa=Symbol.for("react.lazy"),pa=Symbol.for("react.debug_trace_mode"),
J=Symbol.for("react.default_value"),qa=Symbol.for("react.postpone"),K=Symbol.iterator;function ra(a){if(null===a||"object"!==typeof a)return null;a=K&&a[K]||a["@@iterator"];return"function"===typeof a?a:null}var L={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M={};function N(a,b,c){this.props=a;this.context=b;this.refs=M;this.updater=c||L}N.prototype.isReactComponent={};
N.prototype.setState=function(a,b){if("object"!==typeof a&&"function"!==typeof a&&null!=a)throw Error(C(85));this.updater.enqueueSetState(this,a,b,"setState")};N.prototype.forceUpdate=function(a){this.updater.enqueueForceUpdate(this,a,"forceUpdate")};function O(){}O.prototype=N.prototype;function P(a,b,c){this.props=a;this.context=b;this.refs=M;this.updater=c||L}var Q=P.prototype=new O;Q.constructor=P;l(Q,N.prototype);Q.isPureReactComponent=!0;
var R=Array.isArray,S=Object.prototype.hasOwnProperty,T={key:!0,ref:!0,__self:!0,__source:!0};function sa(a,b){return{$$typeof:I,type:a.type,key:b,ref:a.ref,props:a.props,_owner:a._owner}}function U(a){return"object"===typeof a&&null!==a&&a.$$typeof===I}function escape(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(c){return b[c]})}var V=/\/+/g;function W(a,b){return"object"===typeof a&&null!==a&&null!=a.key?escape(""+a.key):b.toString(36)}
function X(a,b,c,d,e){var f=typeof a;if("undefined"===f||"boolean"===f)a=null;var h=!1;if(null===a)h=!0;else switch(f){case "string":case "number":h=!0;break;case "object":switch(a.$$typeof){case I:case da:h=!0}}if(h)return h=a,e=e(h),a=""===d?"."+W(h,0):d,R(e)?(c="",null!=a&&(c=a.replace(V,"$&/")+"/"),X(e,b,c,"",function(m){return m})):null!=e&&(U(e)&&(e=sa(e,c+(!e.key||h&&h.key===e.key?"":(""+e.key).replace(V,"$&/")+"/")+a)),b.push(e)),1;h=0;d=""===d?".":d+":";if(R(a))for(var g=0;g<a.length;g++){f=
a[g];var k=d+W(f,g);h+=X(f,b,c,k,e)}else if(k=ra(a),"function"===typeof k)for(a=k.call(a),g=0;!(f=a.next()).done;)f=f.value,k=d+W(f,g++),h+=X(f,b,c,k,e);else if("object"===f)throw b=String(a),Error(C(31,"[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b));return h}function Y(a,b,c){if(null==a)return a;var d=[],e=0;X(a,d,"","",function(f){return b.call(c,f,e++)});return d}
function ta(a){if(-1===a._status){var b=a._result;b=b();b.then(function(c){if(0===a._status||-1===a._status)a._status=1,a._result=c},function(c){if(0===a._status||-1===a._status)a._status=2,a._result=c});-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result.default;throw a._result;}function ua(){return new WeakMap}function Z(){return{s:0,v:void 0,o:null,p:null}}
exports.Children={map:Y,forEach:function(a,b,c){Y(a,function(){b.apply(this,arguments)},c)},count:function(a){var b=0;Y(a,function(){b++});return b},toArray:function(a){return Y(a,function(b){return b})||[]},only:function(a){if(!U(a))throw Error(C(143));return a}};exports.Fragment=ea;exports.Profiler=ha;exports.StrictMode=fa;exports.Suspense=la;exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=w;exports.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=B;
exports.cache=function(a){return function(){var b=n.current;if(!b)return a.apply(null,arguments);var c=b.getCacheForType(ua);b=c.get(a);void 0===b&&(b=Z(),c.set(a,b));c=0;for(var d=arguments.length;c<d;c++){var e=arguments[c];if("function"===typeof e||"object"===typeof e&&null!==e){var f=b.o;null===f&&(b.o=f=new WeakMap);b=f.get(e);void 0===b&&(b=Z(),f.set(e,b))}else f=b.p,null===f&&(b.p=f=new Map),b=f.get(e),void 0===b&&(b=Z(),f.set(e,b))}if(1===b.s)return b.v;if(2===b.s)throw b.v;try{var h=a.apply(null,
arguments);c=b;c.s=1;return c.v=h}catch(g){throw h=b,h.s=2,h.v=g,g;}}};
exports.cloneElement=function(a,b,c){if(null===a||void 0===a)throw Error(C(267,a));var d=l({},a.props),e=a.key,f=a.ref,h=a._owner;if(null!=b){void 0!==b.ref&&(f=b.ref,h=u.current);void 0!==b.key&&(e=""+b.key);if(a.type&&a.type.defaultProps)var g=a.type.defaultProps;for(k in b)S.call(b,k)&&!T.hasOwnProperty(k)&&(d[k]=void 0===b[k]&&void 0!==g?g[k]:b[k])}var k=arguments.length-2;if(1===k)d.children=c;else if(1<k){g=Array(k);for(var m=0;m<k;m++)g[m]=arguments[m+2];d.children=g}return{$$typeof:I,type:a.type,
key:e,ref:f,props:d,_owner:h}};exports.createElement=function(a,b,c){var d,e={},f=null,h=null;if(null!=b)for(d in void 0!==b.ref&&(h=b.ref),void 0!==b.key&&(f=""+b.key),b)S.call(b,d)&&!T.hasOwnProperty(d)&&(e[d]=b[d]);var g=arguments.length-2;if(1===g)e.children=c;else if(1<g){for(var k=Array(g),m=0;m<g;m++)k[m]=arguments[m+2];e.children=k}if(a&&a.defaultProps)for(d in g=a.defaultProps,g)void 0===e[d]&&(e[d]=g[d]);return{$$typeof:I,type:a,key:f,ref:h,props:e,_owner:u.current}};exports.createRef=function(){return{current:null}};
exports.createServerContext=function(a,b){var c=!0;if(!v[a]){c=!1;var d={$$typeof:ja,_currentValue:b,_currentValue2:b,_defaultValue:b,_threadCount:0,Provider:null,Consumer:null,_globalName:a};d.Provider={$$typeof:ia,_context:d};v[a]=d}d=v[a];if(d._defaultValue===J)d._defaultValue=b,d._currentValue===J&&(d._currentValue=b),d._currentValue2===J&&(d._currentValue2=b);else if(c)throw Error(C(429,a));return d};
exports.experimental_taintObjectReference=function(a,b){a=""+(a||"A tainted value was attempted to be serialized to a Client Component or Action closure. This would leak it to the client.");if("string"===typeof b||"bigint"===typeof b)throw Error(C(496));if(null===b||"object"!==typeof b&&"function"!==typeof b)throw Error(C(497));E.set(b,a)};
exports.experimental_taintUniqueValue=function(a,b,c){a=""+(a||"A tainted value was attempted to be serialized to a Client Component or Action closure. This would leak it to the client.");if(null===b||"object"!==typeof b&&"function"!==typeof b)throw Error(C(493));if("string"!==typeof c&&"bigint"!==typeof c)if(c instanceof ba||c instanceof DataView)G.add(c.byteLength),c=String.fromCharCode.apply(String,new Uint8Array(c.buffer,c.byteOffset,c.byteLength));else{a=null===c?"null":typeof c;if("object"===
a||"function"===a)throw Error(C(494));throw Error(C(495,a));}var d=F.get(c);void 0===d?F.set(c,{message:a,count:1}):d.count++;null!==H&&H.register(b,c)};exports.forwardRef=function(a){return{$$typeof:ka,render:a}};exports.isValidElement=U;exports.lazy=function(a){return{$$typeof:oa,_payload:{_status:-1,_result:a},_init:ta}};exports.memo=function(a,b){return{$$typeof:na,type:a,compare:void 0===b?null:b}};exports.startTransition=function(a){a()};exports.unstable_DebugTracingMode=pa;
exports.unstable_SuspenseList=ma;exports.unstable_getCacheForType=function(a){var b=n.current;return b?b.getCacheForType(a):a()};exports.unstable_getCacheSignal=function(){var a=n.current;if(!a){a=new AbortController;var b=Error(C(455));a.abort(b);return a.signal}return a.getCacheSignal()};exports.unstable_postpone=function(a){a=Error(a);a.$$typeof=qa;throw a;};exports.use=function(a){return t.current.use(a)};exports.useCallback=function(a,b){return t.current.useCallback(a,b)};
exports.useContext=function(a){return t.current.useContext(a)};exports.useDebugValue=function(){};exports.useId=function(){return t.current.useId()};exports.useMemo=function(a,b){return t.current.useMemo(a,b)};exports.version="18.3.0-experimental-8c8ee9ee6-20231026";
