'use client';
import Image from 'next/image';

export default function HeroSection() {
  return (
    <div className="bg-black text-white font-inter">
      <header className="flex items-center justify-between px-10 py-4 border-b border-gray-800">
        <div className="flex items-center space-x-2">
          <Image src="/logo.svg" alt="Praith Logo" width={24} height={24} />
          <span className="text-white font-bold text-xl">Praith</span>
        </div>
        <nav className="flex items-center space-x-8 text-sm font-medium text-white">
          <a href="#" className="text-lime-400">Home</a>
          <a href="#" className="hover:text-lime-400">Product Page</a>
          <a href="#" className="hover:text-lime-400">Contact Us</a>
          <button className="bg-lime-400 text-black font-semibold px-4 py-2 rounded ml-6 hover:bg-lime-500 transition">
            Get Started
          </button>
          <div className="ml-2">
            <select className="bg-transparent text-white">
              <option>ENG</option>
            </select>
          </div>
        </nav>
      </header>

      <section className="relative min-h-[90vh] overflow-hidden">
        <div className="max-w-7xl mx-auto grid md:grid-cols-2 items-center px-10 py-16">
          <div className="space-y-4">
            <div className="inline-block bg-gray-800 px-3 py-1 text-xs rounded-full text-white">
              🔥 100% TRUSTED PLATFORM
            </div>
            <h1 className="text-4xl md:text-5xl font-black leading-tight">
              POWERING GLOBAL PAYMENTS WITH <br />
              <span className="text-lime-400">BLOCKCHAIN</span>
            </h1>
            <p className="text-gray-300 text-base md:text-lg leading-relaxed max-w-md">
              Praith Is A Blockchain-Powered Crypto Payment Gateway That Solves The Traditional Payment Problems. Our Platform Enables Fast, Low-Fee, And Borderless Transactions Using Stablecoins On The Tron And Binance Smart Chain (BEP-20) Networks.
            </p>
            <button className="bg-lime-400 text-black font-semibold px-6 py-3 rounded hover:bg-lime-500 transition">
              Get Started
            </button>
          </div>
          <div className="relative mt-16 md:mt-0">
            <Image src="/globe-grid.svg" alt="World Map" width={500} height={500} className="mx-auto" />
            <div className="absolute top-1/2 left-[52%] transform -translate-x-1/2 -translate-y-1/2">
              <Image src="/binance-icon.svg" alt="Binance" width={48} height={48} />
            </div>
            <Image src="/tether-icon.svg" alt="Tether" width={40} height={40} className="absolute left-0 top-1/3" />
            <Image src="/bitcoin-icon.svg" alt="Bitcoin" width={40} height={40} className="absolute bottom-12 left-1/4" />
            <Image src="/tron-icon.svg" alt="Tron" width={40} height={40} className="absolute top-8 right-[40%]" />
            <Image src="/usdc-icon.svg" alt="USDC" width={40} height={40} className="absolute right-0 top-1/2" />
          </div>
        </div>
        <div className="w-full py-6 bg-gradient-to-t from-black via-black/80 to-transparent flex items-center justify-center space-x-10">
          <div className="flex items-center space-x-2 text-white text-sm">
            <Image src="/tether-logo.svg" alt="Tether" width={20} height={20} />
            <span>tether</span>
          </div>
          <div className="flex items-center space-x-2 text-white text-sm">
            <Image src="/bitcoin-logo.svg" alt="Bitcoin" width={20} height={20} />
            <span>bitcoin</span>
          </div>
          <div className="flex items-center space-x-2 text-white text-sm">
            <Image src="/tron-logo.svg" alt="Tron" width={20} height={20} />
            <span>TRON</span>
          </div>
          <div className="flex items-center space-x-2 text-white text-sm">
            <Image src="/ethereum-logo.svg" alt="Ethereum" width={20} height={20} />
            <span>ethereum</span>
          </div>
          <div className="flex items-center space-x-2 text-white text-sm">
            <Image src="/litecoin-logo.svg" alt="Litecoin" width={20} height={20} />
            <span>litecoin</span>
          </div>
        </div>
      </section>
    </div>
  );
}
