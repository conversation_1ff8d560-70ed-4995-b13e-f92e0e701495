/**
 * @license React
 * react-dom-server-legacy.node.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/*


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var ca=require("next/dist/compiled/react-experimental"),ha=require("react-dom"),ia=require("stream");
function ja(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}
var r=Object.assign,u=Object.prototype.hasOwnProperty,ka=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),la={},xa={};
function ya(a){if(u.call(xa,a))return!0;if(u.call(la,a))return!1;if(ka.test(a))return xa[a]=!0;la[a]=!0;return!1}
var za=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Aa=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ba=/["'&<>]/;
function v(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ba.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ca=/([A-Z])/g,Ia=/^ms-/,Ja=Array.isArray,Ka=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,La={pending:!1,data:null,method:null,action:null},Ma=ha.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,kb={prefetchDNS:Na,preconnect:Oa,preload:fb,preloadModule:gb,preinitStyle:hb,preinitScript:ib,preinitModuleScript:jb},lb=[];
function mb(a,b){var c=0;void 0!==b&&(c=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:c,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function x(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function nb(a){return x("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function ob(a,b,c){switch(b){case "noscript":return x(2,null,a.tagScope|1);case "select":return x(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return x(3,null,a.tagScope);case "picture":return x(2,null,a.tagScope|2);case "math":return x(4,null,a.tagScope);case "foreignObject":return x(2,null,a.tagScope);case "table":return x(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return x(6,null,a.tagScope);case "colgroup":return x(8,null,a.tagScope);case "tr":return x(7,null,a.tagScope)}return 5<=
a.insertionMode?x(2,null,a.tagScope):0===a.insertionMode?"html"===b?x(1,null,a.tagScope):x(2,null,a.tagScope):1===a.insertionMode?x(2,null,a.tagScope):a}var pb=new Map;
function zb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(u.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=v(d);e=v((""+e).trim())}else f=pb.get(d),void 0===f&&(f=v(d.replace(Ca,"-$1").toLowerCase().replace(Ia,"-ms-")),pb.set(d,f)),e="number"===typeof e?0===e||za.has(d)?""+e:e+"px":
v((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}function Ab(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function C(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',v(c),'"')}function Bb(a){var b=a.nextFormID++;return a.idPrefix+b}var Cb=v("javascript:throw new Error('A React form was unexpectedly submitted.')");
function Db(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");C(this,"name",b);C(this,"value",a);this.push("/>")}
function Eb(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Bb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',Cb,'"'),g=f=e=d=h=null,Fb(b,c)));null!=h&&D(a,"name",h);null!=d&&D(a,"formAction",d);null!=e&&D(a,"formEncType",e);null!=f&&D(a,"formMethod",f);null!=g&&D(a,"formTarget",g);return k}
function D(a,b,c){switch(b){case "className":C(a,"class",c);break;case "tabIndex":C(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":C(a,b,c);break;case "style":zb(a,c);break;case "src":case "href":if(""===c)break;case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',v(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Ab(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',v(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',v(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',v(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',v(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',v(c),'"');break;case "xlinkActuate":C(a,"xlink:actuate",
c);break;case "xlinkArcrole":C(a,"xlink:arcrole",c);break;case "xlinkRole":C(a,"xlink:role",c);break;case "xlinkShow":C(a,"xlink:show",c);break;case "xlinkTitle":C(a,"xlink:title",c);break;case "xlinkType":C(a,"xlink:type",c);break;case "xmlBase":C(a,"xml:base",c);break;case "xmlLang":C(a,"xml:lang",c);break;case "xmlSpace":C(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Aa.get(b)||b,ya(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',v(c),'"')}}}function E(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function Gb(a){var b="";ca.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function Fb(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});',"\x3c/script>"))}
function Hb(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return H(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return H(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:v(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:r({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&Ib(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return H(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return H(d.preconnectChunks,b);case "preload":return H(d.preloadChunks,
b);default:return H(d.hoistableChunks,b)}}function H(a,b){a.push(I("link"));for(var c in b)if(u.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:D(a,c,d)}}a.push("/>");return null}
function Jb(a,b,c){a.push(I(c));for(var d in b)if(u.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:D(a,d,e)}}a.push("/>");return null}
function Kb(a,b){a.push(I("title"));var c=null,d=null,e;for(e in b)if(u.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:D(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(v(""+b));E(a,d,c);a.push(Lb("title"));return null}
function Mb(a,b){a.push(I("script"));var c=null,d=null,e;for(e in b)if(u.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:D(a,e,f)}}a.push(">");E(a,d,c);"string"===typeof c&&a.push(v(c));a.push(Lb("script"));return null}
function Nb(a,b,c){a.push(I(c));var d=c=null,e;for(e in b)if(u.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:D(a,e,f)}}a.push(">");E(a,d,c);return"string"===typeof c?(a.push(v(c)),null):c}var Ob=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Pb=new Map;function I(a){var b=Pb.get(a);if(void 0===b){if(!Ob.test(a))throw Error("Invalid tag: "+a);b="<"+a;Pb.set(a,b)}return b}
function Qb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(I("select"));var h=null,k=null,l;for(l in c)if(u.call(c,l)){var p=c[l];if(null!=p)switch(l){case "children":h=p;break;case "dangerouslySetInnerHTML":k=p;break;case "defaultValue":case "value":break;default:D(a,l,p)}}a.push(">");E(a,k,h);return h;case "option":var q=f.selectedValue;a.push(I("option"));var m=null,y=null,A=null,Q=null,N;for(N in c)if(u.call(c,
N)){var w=c[N];if(null!=w)switch(N){case "children":m=w;break;case "selected":A=w;break;case "dangerouslySetInnerHTML":Q=w;break;case "value":y=w;default:D(a,N,w)}}if(null!=q){var n=null!==y?""+y:Gb(m);if(Ja(q))for(var Y=0;Y<q.length;Y++){if(""+q[Y]===n){a.push(' selected=""');break}}else""+q===n&&a.push(' selected=""')}else A&&a.push(' selected=""');a.push(">");E(a,Q,m);return m;case "textarea":a.push(I("textarea"));var F=null,Z=null,t=null,G;for(G in c)if(u.call(c,G)){var z=c[G];if(null!=z)switch(G){case "children":t=
z;break;case "value":F=z;break;case "defaultValue":Z=z;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:D(a,G,z)}}null===F&&null!==Z&&(F=Z);a.push(">");if(null!=t){if(null!=F)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ja(t)){if(1<t.length)throw Error("<textarea> can only have at most one child.");F=""+t[0]}F=""+t}"string"===typeof F&&"\n"===F[0]&&a.push("\n");null!==F&&a.push(v(""+F));
return null;case "input":a.push(I("input"));var O=null,Da=null,K=null,ma=null,da=null,U=null,Pa=null,Qa=null,Ra=null,na;for(na in c)if(u.call(c,na)){var P=c[na];if(null!=P)switch(na){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":O=P;break;case "formAction":Da=P;break;case "formEncType":K=P;break;case "formMethod":ma=P;break;case "formTarget":da=P;break;case "defaultChecked":Ra=
P;break;case "defaultValue":Pa=P;break;case "checked":Qa=P;break;case "value":U=P;break;default:D(a,na,P)}}var qb=Eb(a,d,e,Da,K,ma,da,O);null!==Qa?Ab(a,"checked",Qa):null!==Ra&&Ab(a,"checked",Ra);null!==U?D(a,"value",U):null!==Pa&&D(a,"value",Pa);a.push("/>");null!==qb&&qb.forEach(Db,a);return null;case "button":a.push(I("button"));var oa=null,pa=null,aa=null,qa=null,ra=null,Sa=null,sa=null,Ta;for(Ta in c)if(u.call(c,Ta)){var ba=c[Ta];if(null!=ba)switch(Ta){case "children":oa=ba;break;case "dangerouslySetInnerHTML":pa=
ba;break;case "name":aa=ba;break;case "formAction":qa=ba;break;case "formEncType":ra=ba;break;case "formMethod":Sa=ba;break;case "formTarget":sa=ba;break;default:D(a,Ta,ba)}}var Ic=Eb(a,d,e,qa,ra,Sa,sa,aa);a.push(">");null!==Ic&&Ic.forEach(Db,a);E(a,pa,oa);if("string"===typeof oa){a.push(v(oa));var Jc=null}else Jc=oa;return Jc;case "form":a.push(I("form"));var Ua=null,Kc=null,ea=null,Va=null,Wa=null,Xa=null,Ya;for(Ya in c)if(u.call(c,Ya)){var fa=c[Ya];if(null!=fa)switch(Ya){case "children":Ua=fa;
break;case "dangerouslySetInnerHTML":Kc=fa;break;case "action":ea=fa;break;case "encType":Va=fa;break;case "method":Wa=fa;break;case "target":Xa=fa;break;default:D(a,Ya,fa)}}var Vb=null,Wb=null;if("function"===typeof ea)if("function"===typeof ea.$$FORM_ACTION){var pe=Bb(d),Ea=ea.$$FORM_ACTION(pe);ea=Ea.action||"";Va=Ea.encType;Wa=Ea.method;Xa=Ea.target;Vb=Ea.data;Wb=Ea.name}else a.push(" ","action",'="',Cb,'"'),Xa=Wa=Va=ea=null,Fb(d,e);null!=ea&&D(a,"action",ea);null!=Va&&D(a,"encType",Va);null!=
Wa&&D(a,"method",Wa);null!=Xa&&D(a,"target",Xa);a.push(">");null!==Wb&&(a.push('<input type="hidden"'),C(a,"name",Wb),a.push("/>"),null!==Vb&&Vb.forEach(Db,a));E(a,Kc,Ua);if("string"===typeof Ua){a.push(v(Ua));var Lc=null}else Lc=Ua;return Lc;case "menuitem":a.push(I("menuitem"));for(var rb in c)if(u.call(c,rb)){var Mc=c[rb];if(null!=Mc)switch(rb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:D(a,rb,Mc)}}a.push(">");
return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Nc=Kb(a,c);else Kb(e.hoistableChunks,c),Nc=null;return Nc;case "link":return Hb(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var Xb=c.async;if("string"!==typeof c.src||!c.src||!Xb||"function"===typeof Xb||"symbol"===typeof Xb||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Oc=Mb(a,c);else{var sb=c.src;if("module"===c.type){var tb=d.moduleScriptResources;var Pc=e.preloads.moduleScripts}else tb=
d.scriptResources,Pc=e.preloads.scripts;var ub=tb.hasOwnProperty(sb)?tb[sb]:void 0;if(null!==ub){tb[sb]=null;var Yb=c;if(ub){2===ub.length&&(Yb=r({},c),Ib(Yb,ub));var Qc=Pc.get(sb);Qc&&(Qc.length=0)}var Rc=[];e.scripts.add(Rc);Mb(Rc,Yb)}g&&a.push("\x3c!-- --\x3e");Oc=null}return Oc;case "style":var vb=c.precedence,ta=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof vb||"string"!==typeof ta||""===ta){a.push(I("style"));var Fa=null,Sc=null,Za;for(Za in c)if(u.call(c,
Za)){var wb=c[Za];if(null!=wb)switch(Za){case "children":Fa=wb;break;case "dangerouslySetInnerHTML":Sc=wb;break;default:D(a,Za,wb)}}a.push(">");var $a=Array.isArray(Fa)?2>Fa.length?Fa[0]:null:Fa;"function"!==typeof $a&&"symbol"!==typeof $a&&null!==$a&&void 0!==$a&&a.push(v(""+$a));E(a,Sc,Fa);a.push(Lb("style"));var Tc=null}else{var ua=e.styles.get(vb);if(null!==(d.styleResources.hasOwnProperty(ta)?d.styleResources[ta]:void 0)){d.styleResources[ta]=null;ua?ua.hrefs.push(v(ta)):(ua={precedence:v(vb),
rules:[],hrefs:[v(ta)],sheets:new Map},e.styles.set(vb,ua));var Uc=ua.rules,Ga=null,Vc=null,xb;for(xb in c)if(u.call(c,xb)){var Zb=c[xb];if(null!=Zb)switch(xb){case "children":Ga=Zb;break;case "dangerouslySetInnerHTML":Vc=Zb}}var ab=Array.isArray(Ga)?2>Ga.length?Ga[0]:null:Ga;"function"!==typeof ab&&"symbol"!==typeof ab&&null!==ab&&void 0!==ab&&Uc.push(v(""+ab));E(Uc,Vc,Ga)}ua&&e.boundaryResources&&e.boundaryResources.styles.add(ua);g&&a.push("\x3c!-- --\x3e");Tc=void 0}return Tc;case "meta":if(3===
f.insertionMode||f.tagScope&1||null!=c.itemProp)var Wc=Jb(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),Wc="string"===typeof c.charSet?Jb(e.charsetChunks,c,"meta"):"viewport"===c.name?Jb(e.preconnectChunks,c,"meta"):Jb(e.hoistableChunks,c,"meta");return Wc;case "listing":case "pre":a.push(I(b));var bb=null,cb=null,db;for(db in c)if(u.call(c,db)){var yb=c[db];if(null!=yb)switch(db){case "children":bb=yb;break;case "dangerouslySetInnerHTML":cb=yb;break;default:D(a,db,yb)}}a.push(">");if(null!=cb){if(null!=
bb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof cb||!("__html"in cb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var va=cb.__html;null!==va&&void 0!==va&&("string"===typeof va&&0<va.length&&"\n"===va[0]?a.push("\n",va):a.push(""+va))}"string"===typeof bb&&"\n"===bb[0]&&a.push("\n");return bb;case "img":var L=c.src,
B=c.srcSet;if(!("lazy"===c.loading||!L&&!B||"string"!==typeof L&&null!=L||"string"!==typeof B&&null!=B)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof L||":"!==L[4]||"d"!==L[0]&&"D"!==L[0]||"a"!==L[1]&&"A"!==L[1]||"t"!==L[2]&&"T"!==L[2]||"a"!==L[3]&&"A"!==L[3])&&("string"!==typeof B||":"!==B[4]||"d"!==B[0]&&"D"!==B[0]||"a"!==B[1]&&"A"!==B[1]||"t"!==B[2]&&"T"!==B[2]||"a"!==B[3]&&"A"!==B[3])){var Xc="string"===typeof c.sizes?c.sizes:void 0,eb=B?B+"\n"+(Xc||""):L,$b=e.preloads.images,
wa=$b.get(eb);if(wa){if("high"===c.fetchPriority||10>e.highImagePreloads.size)$b.delete(eb),e.highImagePreloads.add(wa)}else d.imageResources.hasOwnProperty(eb)||(d.imageResources[eb]=lb,wa=[],H(wa,{rel:"preload",as:"image",href:B?void 0:L,imageSrcSet:B,imageSizes:Xc,crossOrigin:c.crossOrigin,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(wa):(e.bulkPreloads.add(wa),$b.set(eb,
wa)))}return Jb(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Jb(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Yc=Nb(e.headChunks,c,"head")}else Yc=Nb(a,c,"head");return Yc;case "html":if(0===
f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[""];var Zc=Nb(e.htmlChunks,c,"html")}else Zc=Nb(a,c,"html");return Zc;default:if(-1!==b.indexOf("-")){a.push(I(b));var ac=null,$c=null,Ha;for(Ha in c)if(u.call(c,Ha)){var R=c[Ha];if(null!=R){var ad=Ha;switch(Ha){case "children":ac=R;break;case "dangerouslySetInnerHTML":$c=R;break;case "style":zb(a,R);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;case "className":ad="class";default:if(ya(Ha)&&"function"!==typeof R&&
"symbol"!==typeof R&&!1!==R){if(!0===R)R="";else if("object"===typeof R)continue;a.push(" ",ad,'="',v(R),'"')}}}}a.push(">");E(a,$c,ac);return ac}}return Nb(a,c,b)}var Rb=new Map;function Lb(a){var b=Rb.get(a);void 0===b&&(b="</"+a+">",Rb.set(a,b));return b}function Sb(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}
function Tb(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function Ub(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error("Unknown insertion mode. This is a bug in React.");}}
function bc(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error("Unknown insertion mode. This is a bug in React.");}}var cc=/[<\u2028\u2029]/g;
function dc(a){return JSON.stringify(a).replace(cc,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var ec=/[&><\u2028\u2029]/g;
function fc(a){return JSON.stringify(a).replace(ec,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var gc=!1,hc=!0;
function ic(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);hc=this.push("</style>");gc=!0;b.length=0;c.length=0}}function jc(a){return 2!==a.state?gc=!0:!1}function kc(a,b,c){gc=!1;hc=!0;b.styles.forEach(ic,a);b.stylesheets.forEach(jc);gc&&(c.stylesToHoist=!0);return hc}
function J(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var lc=[];function mc(a){H(lc,a.props);for(var b=0;b<lc.length;b++)this.push(lc[b]);lc.length=0;a.state=2}
function nc(a){var b=0<a.sheets.size;a.sheets.forEach(mc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function oc(a){if(0===a.state){a.state=1;var b=a.props;H(lc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<lc.length;a++)this.push(lc[a]);lc.length=0}}function pc(a){a.sheets.forEach(oc,this);a.sheets.clear()}
function qc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=fc(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=fc(""+d.props.href);a.push(g);e=""+e;a.push(",");e=fc(e);a.push(e);for(var h in f)if(u.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!ya(h))break a;g=""+g}e.push(",");k=fc(k);e.push(k);e.push(",");g=
fc(g);e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function rc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=v(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=v(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=v(JSON.stringify(e));a.push(e);for(var h in f)if(u.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!ya(h))break a;g=""+g}e.push(",");k=v(JSON.stringify(k));e.push(k);
e.push(",");g=v(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}function Na(a){var b=M?M:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){var e=[];c.dnsResources[a]=null;H(e,{href:a,rel:"dns-prefetch"});d.preconnects.add(e)}sc(b)}}}
function Oa(a,b){var c=M?M:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){d="use-credentials"===b?d.connectResources.credentials:"string"===typeof b?d.connectResources.anonymous:d.connectResources.default;if(!d.hasOwnProperty(a)){var f=[];d[a]=null;H(f,{rel:"preconnect",href:a,crossOrigin:b});e.preconnects.add(f)}sc(c)}}}
function fb(a,b,c){var d=M?M:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}h=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(h))return;e.imageResources[h]=lb;e=[];H(e,r({rel:"preload",href:g?void 0:a,as:b},c));"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(h,e));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];H(g,r({rel:"preload",href:a,
as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?lb:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);H(g,r({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?lb:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=
e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;e=[];c=r({rel:"preload",href:a,as:b},c);switch(b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}H(e,c);g[a]=lb}sc(d)}}}
function gb(a,b){var c=M?M:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?lb:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=lb}H(f,r({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);sc(c)}}}
function hb(a,b,c){var d=M?M:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:v(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:r({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Ib(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),sc(d))}}}
function ib(a,b){var c=M?M:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=r({src:a,async:!0},b),f&&(2===f.length&&Ib(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Mb(a,b),sc(c))}}}
function jb(a,b){var c=M?M:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=r({src:a,type:"module",async:!0},b),f&&(2===f.length&&Ib(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Mb(a,b),sc(c))}}}function Ib(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}function tc(a){this.styles.add(a)}
function uc(a){this.stylesheets.add(a)}
function vc(a,b){var c=a.idPrefix;a=c+"P:";var d=c+"S:";c+="B:";var e=new Set,f=new Set,g=new Set,h=new Map,k=new Set,l=new Set,p=new Set,q={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};return{placeholderPrefix:a,segmentPrefix:d,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:[],charsetChunks:[],preconnectChunks:[],importMapChunks:[],preloadChunks:[],hoistableChunks:[],preconnects:e,fontPreloads:f,
highImagePreloads:g,styles:h,bootstrapScripts:k,scripts:l,bulkPreloads:p,preloads:q,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function wc(a,b,c,d){if(c.generateStaticMarkup)return a.push(v(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(v(b)),a=!0);return a}
var xc=Symbol.for("react.element"),yc=Symbol.for("react.portal"),zc=Symbol.for("react.fragment"),Ac=Symbol.for("react.strict_mode"),Bc=Symbol.for("react.profiler"),Cc=Symbol.for("react.provider"),Dc=Symbol.for("react.context"),Ec=Symbol.for("react.server_context"),Fc=Symbol.for("react.forward_ref"),Gc=Symbol.for("react.suspense"),Hc=Symbol.for("react.suspense_list"),bd=Symbol.for("react.memo"),cd=Symbol.for("react.lazy"),dd=Symbol.for("react.scope"),ed=Symbol.for("react.debug_trace_mode"),fd=Symbol.for("react.offscreen"),
gd=Symbol.for("react.legacy_hidden"),hd=Symbol.for("react.cache"),id=Symbol.for("react.default_value"),jd=Symbol.for("react.memo_cache_sentinel"),kd=Symbol.for("react.postpone"),ld=Symbol.iterator;
function md(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case zc:return"Fragment";case yc:return"Portal";case Bc:return"Profiler";case Ac:return"StrictMode";case Gc:return"Suspense";case Hc:return"SuspenseList";case hd:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Dc:return(a.displayName||"Context")+".Consumer";case Cc:return(a._context.displayName||"Context")+".Provider";case Fc:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case bd:return b=a.displayName||null,null!==b?b:md(a.type)||"Memo";case cd:b=a._payload;a=a._init;try{return md(a(b))}catch(c){break}case Ec:return(a.displayName||a._globalName)+".Provider"}return null}var nd={};function od(a,b){a=a.contextTypes;if(!a)return nd;var c={},d;for(d in a)c[d]=b[d];return c}var pd=null;
function qd(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");qd(a,c)}b.context._currentValue2=b.value}}function rd(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&rd(a)}
function sd(a){var b=a.parent;null!==b&&sd(b);a.context._currentValue2=a.value}function td(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?qd(a,b):td(a,b)}
function ud(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?qd(a,c):ud(a,c);b.context._currentValue2=b.value}function vd(a){var b=pd;b!==a&&(null===b?sd(a):null===a?rd(b):b.depth===a.depth?qd(b,a):b.depth>a.depth?td(b,a):ud(b,a),pd=a)}
var wd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function xd(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=wd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:r({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&wd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=r({},f,h)):r(f,h))}a.state=f}else f.queue=null}
var yd={id:1,overflow:""};function zd(a,b,c){var d=a.id;a=a.overflow;var e=32-Ad(d)-1;d&=~(1<<e);c+=1;var f=32-Ad(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Ad(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Ad=Math.clz32?Math.clz32:Bd,Cd=Math.log,Dd=Math.LN2;function Bd(a){a>>>=0;return 0===a?32:31-(Cd(a)/Dd|0)|0}var Ed=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Fd(){}function Gd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Fd,Fd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Hd=b;throw Ed;}}var Hd=null;
function Id(){if(null===Hd)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Hd;Hd=null;return a}function Jd(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Kd="function"===typeof Object.is?Object.is:Jd,S=null,Ld=null,Md=null,Nd=null,Od=null,T=null,Pd=!1,Qd=!1,Rd=0,Sd=0,Td=-1,Ud=0,Vd=null,Wd=null,Xd=0;
function Yd(){if(null===S)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return S}
function Zd(){if(0<Xd)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function $d(){null===T?null===Od?(Pd=!1,Od=T=Zd()):(Pd=!0,T=Od):null===T.next?(Pd=!1,T=T.next=Zd()):(Pd=!0,T=T.next);return T}function ae(a,b,c,d){for(;Qd;)Qd=!1,Sd=Rd=0,Td=-1,Ud=0,Xd+=1,T=null,c=a(b,d);be();return c}function ce(){var a=Vd;Vd=null;return a}function be(){Nd=Md=Ld=S=null;Qd=!1;Od=null;Xd=0;T=Wd=null}
function de(a,b){return"function"===typeof b?b(a):b}function ee(a,b,c){S=Yd();T=$d();if(Pd){var d=T.queue;b=d.dispatch;if(null!==Wd&&(c=Wd.get(d),void 0!==c)){Wd.delete(d);d=T.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);T.memoizedState=d;return[d,b]}return[T.memoizedState,b]}a=a===de?"function"===typeof b?b():b:void 0!==c?c(b):b;T.memoizedState=a;a=T.queue={last:null,dispatch:null};a=a.dispatch=fe.bind(null,S,a);return[T.memoizedState,a]}
function ge(a,b){S=Yd();T=$d();b=void 0===b?null:b;if(null!==T){var c=T.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Kd(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();T.memoizedState=[a,b];return a}
function fe(a,b,c){if(25<=Xd)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===S)if(Qd=!0,a={action:c,next:null},null===Wd&&(Wd=new Map),c=Wd.get(b),void 0===c)Wd.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function he(){throw Error("A function wrapped in useEffectEvent can't be called during rendering.");}function ie(){throw Error("startTransition cannot be called during server rendering.");}
function je(){throw Error("Cannot update optimistic state while rendering.");}function ke(a){var b=Ud;Ud+=1;null===Vd&&(Vd=[]);return Gd(Vd,a,b)}function le(){throw Error("Cache cannot be refreshed during server rendering.");}function me(){}
var oe={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return ke(a);if(a.$$typeof===Dc||a.$$typeof===Ec)return a._currentValue2}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){Yd();return a._currentValue2},useMemo:ge,useReducer:ee,useRef:function(a){S=Yd();T=$d();var b=T.memoizedState;return null===b?(a={current:a},T.memoizedState=a):b},useState:function(a){return ee(de,a)},
useInsertionEffect:me,useLayoutEffect:me,useCallback:function(a,b){return ge(function(){return a},b)},useImperativeHandle:me,useEffect:me,useDebugValue:me,useDeferredValue:function(a,b){Yd();return void 0!==b?b:a},useTransition:function(){Yd();return[!1,ie]},useId:function(){var a=Ld.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Ad(a)-1)).toString(32)+b;var c=ne;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=Rd++;a=":"+c.idPrefix+
"R"+a;0<b&&(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return le},useEffectEvent:function(){return he},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=jd;return b},useHostTransitionStatus:function(){Yd();return La},useOptimistic:function(a){Yd();return[a,je]},useFormState:function(a,
b,c){Yd();var d=Sd++,e=Md;if("function"===typeof a.$$FORM_ACTION){var f=null,g=Nd;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,null,d]),0),k===f&&(Td=d,b=e[0]))}var l=a.bind(null,b);a=function(q){l(q)};"function"===typeof l.$$FORM_ACTION&&(a.$$FORM_ACTION=function(q){q=l.$$FORM_ACTION(q);void 0!==c&&(c+="",q.action=c);var m=q.data;m&&(null===f&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,
null,d]),0)),m.append("$ACTION_KEY",f));return q});return[b,a]}var p=a.bind(null,b);return[b,function(q){p(q)}]}},ne=null,qe={getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},re=Ka.ReactCurrentDispatcher,se=Ka.ReactCurrentCache;function te(a){console.error(a);return null}function ue(){}
function ve(a,b,c,d,e,f,g,h,k,l,p,q){Ma.current=kb;var m=[],y=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:y,pingedTasks:m,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?te:f,onPostpone:void 0===p?ue:p,onAllReady:void 0===g?
ue:g,onShellReady:void 0===h?ue:h,onShellError:void 0===k?ue:k,onFatalError:void 0===l?ue:l,formState:void 0===q?null:q};c=we(b,0,null,d,!1,!1);c.parentFlushed=!0;a=xe(b,null,a,-1,null,c,y,null,d,nd,null,yd);m.push(a);return b}var M=null;function ye(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,ze(a))}
function Ae(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function xe(a,b,c,d,e,f,g,h,k,l,p,q){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var m={replay:null,node:c,childIndex:d,ping:function(){return ye(a,m)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:p,treeContext:q,thenableState:b};g.add(m);return m}
function Be(a,b,c,d,e,f,g,h,k,l,p,q){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var m={replay:c,node:d,childIndex:e,ping:function(){return ye(a,m)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:p,treeContext:q,thenableState:b};g.add(m);return m}function we(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function V(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function Ce(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function De(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((md(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=r({},c,d)}b.legacyContext=e;W(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,W(a,b,null,f,-1),b.keyPath=e}
function Ee(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=zd(c,1,0),X(a,b,d,-1),b.treeContext=c):h?X(a,b,d,-1):W(a,b,null,d,-1);b.keyPath=f}function Fe(a,b){if(a&&a.defaultProps){b=r({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Ge(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=od(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:d);xd(h,e,f,d);De(a,b,c,h,e)}else{h=od(e,b.legacyContext);S={};Ld=b;Md=a;Nd=c;Sd=Rd=0;Td=-1;Ud=0;Vd=d;d=e(f,h);d=ae(e,f,d,h);g=0!==Rd;var k=Sd,l=Td;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(xd(d,e,f,h),De(a,b,c,d,e)):Ee(a,b,c,d,g,k,l)}else if("string"===typeof e)if(d=
b.blockedSegment,null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=ob(h,e,f),b.keyPath=c,X(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Qb(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;k=b.keyPath;b.formatContext=ob(h,e,f);b.keyPath=c;X(a,b,g,-1);b.formatContext=h;b.keyPath=k;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(Lb(e))}d.lastPushedText=!1}else{switch(e){case gd:case ed:case Ac:case Bc:case zc:e=b.keyPath;b.keyPath=c;W(a,b,null,f.children,-1);b.keyPath=e;return;case fd:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,W(a,b,null,f.children,-1),b.keyPath=e);return;case Hc:e=b.keyPath;b.keyPath=c;W(a,b,null,f.children,-1);b.keyPath=e;return;case dd:throw Error("ReactDOMServer does not yet support scope components.");
case Gc:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{X(a,b,c,-1)}finally{b.keyPath=e}}else{l=b.keyPath;e=b.blockedBoundary;var p=b.blockedSegment;d=f.fallback;var q=f.children;f=new Set;g=Ae(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);k=we(a,p.chunks.length,g,b.formatContext,!1,!1);p.children.push(k);p.lastPushedText=!1;var m=we(a,0,null,b.formatContext,!1,!1);m.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=m;a.renderState.boundaryResources=g.resources;b.keyPath=
c;try{if(X(a,b,q,-1),a.renderState.generateStaticMarkup||m.lastPushedText&&m.textEmbedded&&m.chunks.push("\x3c!-- --\x3e"),m.status=1,He(g,m),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(y){m.status=4,g.status=4,"object"===typeof y&&null!==y&&y.$$typeof===kd?(a.onPostpone(y.message),h="POSTPONE"):h=V(a,y),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=p,b.keyPath=l}h=[c[0],"Suspense Fallback",c[2]];l=a.trackedPostpones;
null!==l&&(p=[h[1],h[2],[],null],l.workingMap.set(h,p),5===g.status?l.workingMap.get(c)[4]=p:g.trackedFallbackNode=p);b=xe(a,null,d,-1,e,k,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Fc:e=e.render;S={};Ld=b;Md=a;Nd=c;Sd=Rd=0;Td=-1;Ud=0;Vd=d;d=e(f,g);f=ae(e,f,d,g);Ee(a,b,c,f,0!==Rd,Sd,Td);return;case bd:e=e.type;f=Fe(e,f);Ge(a,b,c,d,e,f,g);return;case Cc:h=f.children;d=b.keyPath;e=e._context;f=f.value;
g=e._currentValue2;e._currentValue2=f;k=pd;pd=f={parent:k,depth:null===k?0:k.depth+1,context:e,parentValue:g,value:f};b.context=f;b.keyPath=c;W(a,b,null,h,-1);a=pd;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue2=c===id?a.context._defaultValue:c;a=pd=a.parent;b.context=a;b.keyPath=d;return;case Dc:f=f.children;f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;W(a,b,null,f,-1);b.keyPath=e;return;case cd:h=e._init;
e=h(e._payload);f=Fe(e,f);Ge(a,b,c,d,e,f,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==e?e:typeof e)+"."));}}
function Ie(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=we(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,X(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(He(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function W(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Ie(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case xc:var f=d.type,g=d.key,h=d.props,k=d.ref,l=md(f),p=null==g?-1===e?0:e:g;g=[b.keyPath,l,p];if(null!==b.replay)a:{var q=b.replay;e=q.nodes;for(d=0;d<e.length;d++){var m=e[d];if(p===m[1]){if(4===m.length){if(null!==l&&l!==m[0])throw Error("Expected the resume to render <"+m[0]+"> in this slot but instead it rendered <"+
l+">. The tree doesn't match so React will fallback to client rendering.");l=m[2];m=m[3];p=b.node;b.replay={nodes:l,slots:m,pendingTasks:1};try{Ge(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(n){if("object"===typeof n&&null!==n&&(n===Ed||"function"===typeof n.then))throw b.node===p&&(b.replay=q),n;b.replay.pendingTasks--;
Je(a,b.blockedBoundary,n,l,m)}b.replay=q}else{if(f!==Gc)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(md(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{c=void 0;f=m[5];k=m[2];q=m[3];l=null===m[4]?[]:m[4][2];m=null===m[4]?null:m[4][3];p=b.keyPath;var y=b.replay,A=b.blockedBoundary,Q=h.children;h=h.fallback;var N=new Set,w=Ae(a,N);w.parentFlushed=!0;w.rootSegmentID=f;b.blockedBoundary=w;b.replay={nodes:k,slots:q,
pendingTasks:1};a.renderState.boundaryResources=w.resources;try{X(a,b,Q,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===w.pendingTasks&&0===w.status){w.status=1;a.completedBoundaries.push(w);break b}}catch(n){w.status=4,"object"===typeof n&&null!==n&&n.$$typeof===kd?(a.onPostpone(n.message),c="POSTPONE"):c=V(a,n),
w.errorDigest=c,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(w)}finally{a.renderState.boundaryResources=A?A.resources:null,b.blockedBoundary=A,b.replay=y,b.keyPath=p}h=Be(a,null,{nodes:l,slots:m,pendingTasks:0},h,-1,A,N,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(h)}}e.splice(d,1);break a}}}else Ge(a,b,g,c,f,h,k);return;case yc:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");
case cd:h=d._init;d=h(d._payload);W(a,b,null,d,e);return}if(Ja(d)){Ke(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=ld&&d[ld]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);Ke(a,b,g,e)}return}if("function"===typeof d.then)return W(a,b,null,ke(d),e);if(d.$$typeof===Dc||d.$$typeof===Ec)return W(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+
("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=wc(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=wc(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function Ke(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{Ke(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(p){if("object"===typeof p&&
null!==p&&(p===Ed||"function"===typeof p.then))throw p;b.replay.pendingTasks--;Je(a,b.blockedBoundary,p,d,k)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(k=0;k<g;k++){d=c[k];b.treeContext=zd(f,g,k);var l=h[k];"number"===typeof l?(Ie(a,b,l,d,k),delete h[k]):X(a,b,d,k)}b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)k=c[h],b.treeContext=zd(f,g,h),X(a,b,k,h);b.treeContext=f;b.keyPath=e}
function Le(a,b,c,d){d.status=5;var e=c.keyPath,f=c.blockedBoundary;if(null===f)d.id=a.nextSegmentId++,b.rootSlots=d.id,null!==a.completedRootSegment&&(a.completedRootSegment.status=5);else{if(null!==f&&0===f.status){f.status=5;f.rootSegmentID=a.nextSegmentId++;var g=f.trackedContentKeyPath;if(null===g)throw Error("It should not be possible to postpone at the root. This is a bug in React.");var h=f.trackedFallbackNode,k=[];if(g===e&&-1===c.childIndex){d.id=f.rootSegmentID;d=[g[1],g[2],k,f.rootSegmentID,
h,f.rootSegmentID];b.workingMap.set(g,d);Me(d,g[0],b);return}var l=b.workingMap.get(g);void 0===l?(l=[g[1],g[2],k,null,h,f.rootSegmentID],b.workingMap.set(g,l),Me(l,g[0],b)):(g=l,g[4]=h,g[5]=f.rootSegmentID)}-1===d.id&&(d.id=d.parentFlushed&&null!==f?f.rootSegmentID:a.nextSegmentId++);if(-1===c.childIndex)null===e?b.rootSlots=d.id:(c=b.workingMap.get(e),void 0===c?(c=[e[1],e[2],[],d.id],Me(c,e[0],b)):c[3]=d.id);else{if(null===e)if(a=b.rootSlots,null===a)a=b.rootSlots={};else{if("number"===typeof a)throw Error("It should not be possible to postpone both at the root of an element as well as a slot below. This is a bug in React.");
}else if(f=b.workingMap,g=f.get(e),void 0===g)a={},g=[e[1],e[2],[],a],f.set(e,g),Me(g,e[0],b);else if(a=g[3],null===a)a=g[3]={};else if("number"===typeof a)throw Error("It should not be possible to postpone both at the root of an element as well as a slot below. This is a bug in React.");a[c.childIndex]=d.id}}}
function X(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.blockedSegment;if(null===l)try{return W(a,b,null,c,d)}catch(m){if(be(),d=m===Ed?Id():m,"object"===typeof d&&null!==d&&"function"===typeof d.then){c=d;d=ce();a=Be(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;vd(g);return}}else{var p=
l.children.length,q=l.chunks.length;try{return W(a,b,null,c,d)}catch(m){if(be(),l.children.length=p,l.chunks.length=q,d=m===Ed?Id():m,"object"===typeof d&&null!==d){if("function"===typeof d.then){c=d;d=ce();l=b.blockedSegment;p=we(a,l.chunks.length,null,b.formatContext,l.lastPushedText,!0);l.children.push(p);l.lastPushedText=!1;a=xe(a,d,b.node,b.childIndex,b.blockedBoundary,p,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;vd(g);return}if(null!==a.trackedPostpones&&d.$$typeof===kd&&null!==b.blockedBoundary){c=a.trackedPostpones;a.onPostpone(d.message);d=b.blockedSegment;l=we(a,d.chunks.length,null,b.formatContext,d.lastPushedText,!0);d.children.push(l);d.lastPushedText=!1;Le(a,c,b,l);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;vd(g);return}}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;vd(g);throw d;}
function Je(a,b,c,d,e){if("object"===typeof c&&null!==c&&c.$$typeof===kd){a.onPostpone(c.message);var f="POSTPONE"}else f=V(a,c);Ne(a,b,d,e,c,f)}function Oe(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,Pe(this,b,a))}
function Ne(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Ne(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,p=Ae(k,new Set);p.parentFlushed=!0;p.rootSegmentID=h;p.status=4;p.errorDigest=l;p.parentFlushed&&k.clientRenderedBoundaries.push(p)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var q in d)delete d[q]}}
function Qe(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(1!==b.status&&2!==b.status){a=a.replay;if(null===a){V(b,c);Ce(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=V(b,c),Ne(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&(b.onShellError=ue,a=b.onShellReady,a())}}else d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=V(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return Qe(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&(a=b.onAllReady,a())}function He(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&He(a,c)}else a.completedSegments.push(b)}
function Pe(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&(a.onShellError=ue,b=a.onShellReady,b())}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&He(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(Oe,
a),b.fallbackAbortableTasks.clear())):null!==c&&c.parentFlushed&&1===c.status&&(He(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&(a=a.onAllReady,a())}
function ze(a){if(2!==a.status){var b=pd,c=re.current;re.current=oe;var d=se.current;se.current=qe;var e=M;M=a;var f=ne;ne=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,p=k.blockedBoundary;l.renderState.boundaryResources=p?p.resources:null;var q=k.blockedSegment;if(null===q){var m=l;if(0!==k.replay.pendingTasks){vd(k.context);try{var y=k.thenableState;k.thenableState=null;W(m,k,y,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);Pe(m,k.blockedBoundary,null)}catch(K){be();var A=K===Ed?Id():K;if("object"===typeof A&&null!==A&&"function"===typeof A.then){var Q=k.ping;A.then(Q,Q);k.thenableState=ce()}else{k.replay.pendingTasks--;k.abortSet.delete(k);Je(m,k.blockedBoundary,A,k.replay.nodes,k.replay.slots);m.pendingRootTasks--;if(0===m.pendingRootTasks){m.onShellError=ue;var N=m.onShellReady;N()}m.allPendingTasks--;if(0===m.allPendingTasks){var w=m.onAllReady;w()}}}finally{m.renderState.boundaryResources=
null}}}else a:{m=void 0;var n=q;if(0===n.status){vd(k.context);var Y=n.children.length,F=n.chunks.length;try{var Z=k.thenableState;k.thenableState=null;W(l,k,Z,k.node,k.childIndex);l.renderState.generateStaticMarkup||n.lastPushedText&&n.textEmbedded&&n.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);n.status=1;Pe(l,k.blockedBoundary,n)}catch(K){be();n.children.length=Y;n.chunks.length=F;var t=K===Ed?Id():K;if("object"===typeof t&&null!==t){if("function"===typeof t.then){var G=k.ping;t.then(G,G);
k.thenableState=ce();break a}if(null!==l.trackedPostpones&&t.$$typeof===kd){var z=l.trackedPostpones;k.abortSet.delete(k);l.onPostpone(t.message);Le(l,z,k,n);Pe(l,k.blockedBoundary,n);break a}}k.abortSet.delete(k);n.status=4;var O=k.blockedBoundary;"object"===typeof t&&null!==t&&t.$$typeof===kd?(l.onPostpone(t.message),m="POSTPONE"):m=V(l,t);null===O?Ce(l,t):(O.pendingTasks--,4!==O.status&&(O.status=4,O.errorDigest=m,O.parentFlushed&&l.clientRenderedBoundaries.push(O)));l.allPendingTasks--;if(0===
l.allPendingTasks){var Da=l.onAllReady;Da()}}finally{l.renderState.boundaryResources=null}}}}g.splice(0,h);null!==a.destination&&Re(a,a.destination)}catch(K){V(a,K),Ce(a,K)}finally{ne=f,re.current=c,se.current=d,c===oe&&vd(b),M=e}}}
function Se(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=Te(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function Te(a,b,c){var d=c.boundary;if(null===d)return Se(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=v(d),b.push(d),b.push('"')),b.push("></template>")),Se(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Tb(b,a.renderState,
d.rootSegmentID),Se(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Tb(b,a.renderState,d.rootSegmentID),Se(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(tc,e),c.stylesheets.forEach(uc,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");
Te(a,b,c[0]);a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e");return a}function Ue(a,b,c){Ub(b,a.renderState,c.parentFormatContext,c.id);Te(a,b,c);return bc(b,c.parentFormatContext)}
function Ve(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)We(a,b,c,d[e]);d.length=0;kc(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),qc(b,c)):(b.push('" data-sty="'),rc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Sb(b,a)&&d}
function We(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return Ue(a,b,d)}if(e===c.rootSegmentID)return Ue(a,b,d);Ue(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function Re(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,p=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(p)for(f=0;f<p.length;f++)b.push(p[f]);else{var q=I("head");b.push(q);
b.push(">")}}else if(p)for(f=0;f<p.length;f++)b.push(p[f]);var m=e.charsetChunks;for(f=0;f<m.length;f++)b.push(m[f]);m.length=0;e.preconnects.forEach(J,b);e.preconnects.clear();var y=e.preconnectChunks;for(f=0;f<y.length;f++)b.push(y[f]);y.length=0;e.fontPreloads.forEach(J,b);e.fontPreloads.clear();e.highImagePreloads.forEach(J,b);e.highImagePreloads.clear();e.styles.forEach(nc,b);var A=e.importMapChunks;for(f=0;f<A.length;f++)b.push(A[f]);A.length=0;e.bootstrapScripts.forEach(J,b);e.scripts.forEach(J,
b);e.scripts.clear();e.bulkPreloads.forEach(J,b);e.bulkPreloads.clear();var Q=e.preloadChunks;for(f=0;f<Q.length;f++)b.push(Q[f]);Q.length=0;var N=e.hoistableChunks;for(f=0;f<N.length;f++)b.push(N[f]);N.length=0;if(l&&null===p){var w=Lb("head");b.push(w)}Te(a,b,d);a.completedRootSegment=null;Sb(b,a.renderState)}else return;var n=a.renderState;d=0;n.preconnects.forEach(J,b);n.preconnects.clear();var Y=n.preconnectChunks;for(d=0;d<Y.length;d++)b.push(Y[d]);Y.length=0;n.fontPreloads.forEach(J,b);n.fontPreloads.clear();
n.highImagePreloads.forEach(J,b);n.highImagePreloads.clear();n.styles.forEach(pc,b);n.scripts.forEach(J,b);n.scripts.clear();n.bulkPreloads.forEach(J,b);n.bulkPreloads.clear();var F=n.preloadChunks;for(d=0;d<F.length;d++)b.push(F[d]);F.length=0;var Z=n.hoistableChunks;for(d=0;d<Z.length;d++)b.push(Z[d]);Z.length=0;var t=a.clientRenderedBoundaries;for(c=0;c<t.length;c++){var G=t[c];n=b;var z=a.resumableState,O=a.renderState,Da=G.rootSegmentID,K=G.errorDigest,ma=G.errorMessage,da=G.errorComponentStack,
U=0===z.streamingFormat;U?(n.push(O.startInlineScript),0===(z.instructions&4)?(z.instructions|=4,n.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):n.push('$RX("')):n.push('<template data-rxi="" data-bid="');n.push(O.boundaryPrefix);var Pa=Da.toString(16);n.push(Pa);U&&n.push('"');if(K||ma||da)if(U){n.push(",");var Qa=dc(K||"");n.push(Qa)}else{n.push('" data-dgst="');
var Ra=v(K||"");n.push(Ra)}if(ma||da)if(U){n.push(",");var na=dc(ma||"");n.push(na)}else{n.push('" data-msg="');var P=v(ma||"");n.push(P)}if(da)if(U){n.push(",");var qb=dc(da);n.push(qb)}else{n.push('" data-stck="');var oa=v(da);n.push(oa)}if(U?!n.push(")\x3c/script>"):!n.push('"></template>')){a.destination=null;c++;t.splice(0,c);return}}t.splice(0,c);var pa=a.completedBoundaries;for(c=0;c<pa.length;c++)if(!Ve(a,b,pa[c])){a.destination=null;c++;pa.splice(0,c);return}pa.splice(0,c);var aa=a.partialBoundaries;
for(c=0;c<aa.length;c++){var qa=aa[c];a:{t=a;G=b;t.renderState.boundaryResources=qa.resources;var ra=qa.completedSegments;for(z=0;z<ra.length;z++)if(!We(t,G,qa,ra[z])){z++;ra.splice(0,z);var Sa=!1;break a}ra.splice(0,z);Sa=kc(G,qa.resources,t.renderState)}if(!Sa){a.destination=null;c++;aa.splice(0,c);return}}aa.splice(0,c);var sa=a.completedBoundaries;for(c=0;c<sa.length;c++)if(!Ve(a,b,sa[c])){a.destination=null;c++;sa.splice(0,c);return}sa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&
0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,null===a.trackedPostpones&&(c=a.resumableState,c.hasBody&&(aa=Lb("body"),b.push(aa)),c.hasHtml&&(c=Lb("html"),b.push(c))),b.push(null),a.destination=null)}}function sc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?Re(a,b):a.flushScheduled=!1}}
function Xe(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{Re(a,b)}catch(c){V(a,c),Ce(a,c)}}}function Ye(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return Qe(e,a,d)});c.clear()}null!==a.destination&&Re(a,a.destination)}catch(e){V(a,e),Ce(a,e)}}
function Me(a,b,c){if(null===b)c.rootNodes.push(a);else{var d=c.workingMap,e=d.get(b);void 0===e&&(e=[b[1],b[2],[],null],d.set(b,e),Me(e,b[0],c));e[2].push(a)}}function Ze(){}
function $e(a,b,c,d){var e=!1,f=null,g="",h=!1;b=mb(b?b.identifierPrefix:void 0,void 0);a=ve(a,b,vc(b,c),nb(),Infinity,Ze,void 0,function(){h=!0},void 0,void 0,void 0);a.flushScheduled=null!==a.destination;ze(a);Ye(a,d);Xe(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");return g}
function af(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;a.__proto__=b}var bf=function(a){function b(){var d=a.call(this,{})||this;d.request=null;d.startedFlowing=!1;return d}af(b,a);var c=b.prototype;c._destroy=function(d,e){Ye(this.request);e(d)};c._read=function(){this.startedFlowing&&Xe(this.request,this)};return b}(ia.Readable);function cf(){}
function df(a,b){var c=new bf;b=mb(b?b.identifierPrefix:void 0,void 0);var d=ve(a,b,vc(b,!1),nb(),Infinity,cf,function(){c.startedFlowing=!0;Xe(d,c)},void 0,void 0,void 0);c.request=d;d.flushScheduled=null!==d.destination;ze(d);return c}exports.renderToNodeStream=function(a,b){return df(a,b)};exports.renderToStaticMarkup=function(a,b){return $e(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.renderToStaticNodeStream=function(a,b){return df(a,b)};exports.renderToString=function(a,b){return $e(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.version="18.3.0-experimental-8c8ee9ee6-20231026";
