/**
 * @license React
 * react-dom-server-legacy.browser.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/*


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var ea=require("next/dist/compiled/react"),ia=require("react-dom");function n(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function ja(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}
var t=Object.assign,v=Object.prototype.hasOwnProperty,ka=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),la={},ma={};
function za(a){if(v.call(ma,a))return!0;if(v.call(la,a))return!1;if(ka.test(a))return ma[a]=!0;la[a]=!0;return!1}
var Aa=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Ba=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ca=/["'&<>]/;
function w(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ca.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Da=/([A-Z])/g,Ia=/^ms-/,Ja=Array.isArray,Ka=ea.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,La={pending:!1,data:null,method:null,action:null},Ma=ia.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,kb={prefetchDNS:Na,preconnect:eb,preload:fb,preloadModule:gb,preinitStyle:hb,preinitScript:ib,preinitModuleScript:jb},lb=[];
function mb(a,b){var c=0;void 0!==b&&(c=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:c,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function y(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function nb(a,b,c){switch(b){case "noscript":return y(2,null,a.tagScope|1);case "select":return y(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return y(3,null,a.tagScope);case "picture":return y(2,null,a.tagScope|2);case "math":return y(4,null,a.tagScope);case "foreignObject":return y(2,null,a.tagScope);case "table":return y(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return y(6,null,a.tagScope);case "colgroup":return y(8,null,a.tagScope);case "tr":return y(7,null,a.tagScope)}return 5<=
a.insertionMode?y(2,null,a.tagScope):0===a.insertionMode?"html"===b?y(1,null,a.tagScope):y(2,null,a.tagScope):1===a.insertionMode?y(2,null,a.tagScope):a}var ob=new Map;
function pb(a,b){if("object"!==typeof b)throw Error(n(62));var c=!0,d;for(d in b)if(v.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=w(d);e=w((""+e).trim())}else f=ob.get(d),void 0===f&&(f=w(d.replace(Da,"-$1").toLowerCase().replace(Ia,"-ms-")),ob.set(d,f)),e="number"===typeof e?0===e||Aa.has(d)?""+e:e+"px":w((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}
function zb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function E(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',w(c),'"')}function Ab(a){var b=a.nextFormID++;return a.idPrefix+b}var Bb=w("javascript:throw new Error('A React form was unexpectedly submitted.')");function Cb(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error(n(480));E(this,"name",b);E(this,"value",a);this.push("/>")}
function Db(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Ab(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',Bb,'"'),g=f=e=d=h=null,Eb(b,c)));null!=h&&F(a,"name",h);null!=d&&F(a,"formAction",d);null!=e&&F(a,"formEncType",e);null!=f&&F(a,"formMethod",f);null!=g&&F(a,"formTarget",g);return k}
function F(a,b,c){switch(b){case "className":E(a,"class",c);break;case "tabIndex":E(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":E(a,b,c);break;case "style":pb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',w(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":zb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',w(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',w(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',w(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',w(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',w(c),'"');break;case "xlinkActuate":E(a,"xlink:actuate",
c);break;case "xlinkArcrole":E(a,"xlink:arcrole",c);break;case "xlinkRole":E(a,"xlink:role",c);break;case "xlinkShow":E(a,"xlink:show",c);break;case "xlinkTitle":E(a,"xlink:title",c);break;case "xlinkType":E(a,"xlink:type",c);break;case "xmlBase":E(a,"xml:base",c);break;case "xmlLang":E(a,"xml:lang",c);break;case "xmlSpace":E(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Ba.get(b)||b,za(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',w(c),'"')}}}function G(a,b,c){if(null!=b){if(null!=c)throw Error(n(60));if("object"!==typeof b||!("__html"in b))throw Error(n(61));b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}function Fb(a){var b="";ea.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function Eb(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});',"\x3c/script>"))}
function Gb(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return J(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return J(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:w(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:t({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&Hb(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return J(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return J(d.preconnectChunks,b);case "preload":return J(d.preloadChunks,
b);default:return J(d.hoistableChunks,b)}}function J(a,b){a.push(K("link"));for(var c in b)if(v.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error(n(399,"link"));default:F(a,c,d)}}a.push("/>");return null}function Ib(a,b,c){a.push(K(c));for(var d in b)if(v.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(n(399,c));default:F(a,d,e)}}a.push("/>");return null}
function Jb(a,b){a.push(K("title"));var c=null,d=null,e;for(e in b)if(v.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:F(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(w(""+b));G(a,d,c);a.push(Kb("title"));return null}
function Lb(a,b){a.push(K("script"));var c=null,d=null,e;for(e in b)if(v.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:F(a,e,f)}}a.push(">");G(a,d,c);"string"===typeof c&&a.push(w(c));a.push(Kb("script"));return null}
function Mb(a,b,c){a.push(K(c));var d=c=null,e;for(e in b)if(v.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:F(a,e,f)}}a.push(">");G(a,d,c);return"string"===typeof c?(a.push(w(c)),null):c}var Nb=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Ob=new Map;function K(a){var b=Ob.get(a);if(void 0===b){if(!Nb.test(a))throw Error(n(65,a));b="<"+a;Ob.set(a,b)}return b}
function Pb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(K("select"));var h=null,k=null,l;for(l in c)if(v.call(c,l)){var p=c[l];if(null!=p)switch(l){case "children":h=p;break;case "dangerouslySetInnerHTML":k=p;break;case "defaultValue":case "value":break;default:F(a,l,p)}}a.push(">");G(a,k,h);return h;case "option":var r=f.selectedValue;a.push(K("option"));var m=null,H=null,B=null,S=null,u;for(u in c)if(v.call(c,
u)){var x=c[u];if(null!=x)switch(u){case "children":m=x;break;case "selected":B=x;break;case "dangerouslySetInnerHTML":S=x;break;case "value":H=x;default:F(a,u,x)}}if(null!=r){var q=null!==H?""+H:Fb(m);if(Ja(r))for(var Z=0;Z<r.length;Z++){if(""+r[Z]===q){a.push(' selected=""');break}}else""+r===q&&a.push(' selected=""')}else B&&a.push(' selected=""');a.push(">");G(a,S,m);return m;case "textarea":a.push(K("textarea"));var I=null,aa=null,C=null,M;for(M in c)if(v.call(c,M)){var z=c[M];if(null!=z)switch(M){case "children":C=
z;break;case "value":I=z;break;case "defaultValue":aa=z;break;case "dangerouslySetInnerHTML":throw Error(n(91));default:F(a,M,z)}}null===I&&null!==aa&&(I=aa);a.push(">");if(null!=C){if(null!=I)throw Error(n(92));if(Ja(C)){if(1<C.length)throw Error(n(93));I=""+C[0]}I=""+C}"string"===typeof I&&"\n"===I[0]&&a.push("\n");null!==I&&a.push(w(""+I));return null;case "input":a.push(K("input"));var na=null,T=null,ba=null,N=null,W=null,A=null,Oa=null,Pa=null,Qa=null,oa;for(oa in c)if(v.call(c,oa)){var Q=c[oa];
if(null!=Q)switch(oa){case "children":case "dangerouslySetInnerHTML":throw Error(n(399,"input"));case "name":na=Q;break;case "formAction":T=Q;break;case "formEncType":ba=Q;break;case "formMethod":N=Q;break;case "formTarget":W=Q;break;case "defaultChecked":Qa=Q;break;case "defaultValue":Oa=Q;break;case "checked":Pa=Q;break;case "value":A=Q;break;default:F(a,oa,Q)}}var qb=Db(a,d,e,T,ba,N,W,na);null!==Pa?zb(a,"checked",Pa):null!==Qa&&zb(a,"checked",Qa);null!==A?F(a,"value",A):null!==Oa&&F(a,"value",
Oa);a.push("/>");null!==qb&&qb.forEach(Cb,a);return null;case "button":a.push(K("button"));var pa=null,qa=null,ca=null,ra=null,sa=null,Ra=null,ta=null,Sa;for(Sa in c)if(v.call(c,Sa)){var da=c[Sa];if(null!=da)switch(Sa){case "children":pa=da;break;case "dangerouslySetInnerHTML":qa=da;break;case "name":ca=da;break;case "formAction":ra=da;break;case "formEncType":sa=da;break;case "formMethod":Ra=da;break;case "formTarget":ta=da;break;default:F(a,Sa,da)}}var Bc=Db(a,d,e,ra,sa,Ra,ta,ca);a.push(">");null!==
Bc&&Bc.forEach(Cb,a);G(a,qa,pa);if("string"===typeof pa){a.push(w(pa));var Cc=null}else Cc=pa;return Cc;case "form":a.push(K("form"));var Ta=null,Dc=null,fa=null,Ua=null,Va=null,Wa=null,Xa;for(Xa in c)if(v.call(c,Xa)){var ha=c[Xa];if(null!=ha)switch(Xa){case "children":Ta=ha;break;case "dangerouslySetInnerHTML":Dc=ha;break;case "action":fa=ha;break;case "encType":Ua=ha;break;case "method":Va=ha;break;case "target":Wa=ha;break;default:F(a,Xa,ha)}}var Ub=null,Vb=null;if("function"===typeof fa)if("function"===
typeof fa.$$FORM_ACTION){var ee=Ab(d),Ea=fa.$$FORM_ACTION(ee);fa=Ea.action||"";Ua=Ea.encType;Va=Ea.method;Wa=Ea.target;Ub=Ea.data;Vb=Ea.name}else a.push(" ","action",'="',Bb,'"'),Wa=Va=Ua=fa=null,Eb(d,e);null!=fa&&F(a,"action",fa);null!=Ua&&F(a,"encType",Ua);null!=Va&&F(a,"method",Va);null!=Wa&&F(a,"target",Wa);a.push(">");null!==Vb&&(a.push('<input type="hidden"'),E(a,"name",Vb),a.push("/>"),null!==Ub&&Ub.forEach(Cb,a));G(a,Dc,Ta);if("string"===typeof Ta){a.push(w(Ta));var Ec=null}else Ec=Ta;return Ec;
case "menuitem":a.push(K("menuitem"));for(var rb in c)if(v.call(c,rb)){var Fc=c[rb];if(null!=Fc)switch(rb){case "children":case "dangerouslySetInnerHTML":throw Error(n(400));default:F(a,rb,Fc)}}a.push(">");return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Gc=Jb(a,c);else Jb(e.hoistableChunks,c),Gc=null;return Gc;case "link":return Gb(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var Wb=c.async;if("string"!==typeof c.src||!c.src||!Wb||"function"===typeof Wb||
"symbol"===typeof Wb||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Hc=Lb(a,c);else{var sb=c.src;if("module"===c.type){var tb=d.moduleScriptResources;var Ic=e.preloads.moduleScripts}else tb=d.scriptResources,Ic=e.preloads.scripts;var ub=tb.hasOwnProperty(sb)?tb[sb]:void 0;if(null!==ub){tb[sb]=null;var Xb=c;if(ub){2===ub.length&&(Xb=t({},c),Hb(Xb,ub));var Jc=Ic.get(sb);Jc&&(Jc.length=0)}var Kc=[];e.scripts.add(Kc);Lb(Kc,Xb)}g&&a.push("\x3c!-- --\x3e");Hc=null}return Hc;
case "style":var vb=c.precedence,ua=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof vb||"string"!==typeof ua||""===ua){a.push(K("style"));var Fa=null,Lc=null,Ya;for(Ya in c)if(v.call(c,Ya)){var wb=c[Ya];if(null!=wb)switch(Ya){case "children":Fa=wb;break;case "dangerouslySetInnerHTML":Lc=wb;break;default:F(a,Ya,wb)}}a.push(">");var Za=Array.isArray(Fa)?2>Fa.length?Fa[0]:null:Fa;"function"!==typeof Za&&"symbol"!==typeof Za&&null!==Za&&void 0!==Za&&a.push(w(""+Za));G(a,
Lc,Fa);a.push(Kb("style"));var Mc=null}else{var va=e.styles.get(vb);if(null!==(d.styleResources.hasOwnProperty(ua)?d.styleResources[ua]:void 0)){d.styleResources[ua]=null;va?va.hrefs.push(w(ua)):(va={precedence:w(vb),rules:[],hrefs:[w(ua)],sheets:new Map},e.styles.set(vb,va));var Nc=va.rules,Ga=null,Oc=null,xb;for(xb in c)if(v.call(c,xb)){var Yb=c[xb];if(null!=Yb)switch(xb){case "children":Ga=Yb;break;case "dangerouslySetInnerHTML":Oc=Yb}}var $a=Array.isArray(Ga)?2>Ga.length?Ga[0]:null:Ga;"function"!==
typeof $a&&"symbol"!==typeof $a&&null!==$a&&void 0!==$a&&Nc.push(w(""+$a));G(Nc,Oc,Ga)}va&&e.boundaryResources&&e.boundaryResources.styles.add(va);g&&a.push("\x3c!-- --\x3e");Mc=void 0}return Mc;case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Pc=Ib(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),Pc="string"===typeof c.charSet?Ib(e.charsetChunks,c,"meta"):"viewport"===c.name?Ib(e.preconnectChunks,c,"meta"):Ib(e.hoistableChunks,c,"meta");return Pc;case "listing":case "pre":a.push(K(b));
var ab=null,bb=null,cb;for(cb in c)if(v.call(c,cb)){var yb=c[cb];if(null!=yb)switch(cb){case "children":ab=yb;break;case "dangerouslySetInnerHTML":bb=yb;break;default:F(a,cb,yb)}}a.push(">");if(null!=bb){if(null!=ab)throw Error(n(60));if("object"!==typeof bb||!("__html"in bb))throw Error(n(61));var wa=bb.__html;null!==wa&&void 0!==wa&&("string"===typeof wa&&0<wa.length&&"\n"===wa[0]?a.push("\n",wa):a.push(""+wa))}"string"===typeof ab&&"\n"===ab[0]&&a.push("\n");return ab;case "img":var O=c.src,D=
c.srcSet;if(!("lazy"===c.loading||!O&&!D||"string"!==typeof O&&null!=O||"string"!==typeof D&&null!=D)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof O||":"!==O[4]||"d"!==O[0]&&"D"!==O[0]||"a"!==O[1]&&"A"!==O[1]||"t"!==O[2]&&"T"!==O[2]||"a"!==O[3]&&"A"!==O[3])&&("string"!==typeof D||":"!==D[4]||"d"!==D[0]&&"D"!==D[0]||"a"!==D[1]&&"A"!==D[1]||"t"!==D[2]&&"T"!==D[2]||"a"!==D[3]&&"A"!==D[3])){var Qc="string"===typeof c.sizes?c.sizes:void 0,db=D?D+"\n"+(Qc||""):O,Zb=e.preloads.images,
xa=Zb.get(db);if(xa){if("high"===c.fetchPriority||10>e.highImagePreloads.size)Zb.delete(db),e.highImagePreloads.add(xa)}else d.imageResources.hasOwnProperty(db)||(d.imageResources[db]=lb,xa=[],J(xa,{rel:"preload",as:"image",href:D?void 0:O,imageSrcSet:D,imageSizes:Qc,crossOrigin:c.crossOrigin,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(xa):(e.bulkPreloads.add(xa),Zb.set(db,
xa)))}return Ib(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Ib(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Rc=Mb(e.headChunks,c,"head")}else Rc=Mb(a,c,"head");return Rc;case "html":if(0===
f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[""];var Sc=Mb(e.htmlChunks,c,"html")}else Sc=Mb(a,c,"html");return Sc;default:if(-1!==b.indexOf("-")){a.push(K(b));var $b=null,Tc=null,Ha;for(Ha in c)if(v.call(c,Ha)){var ya=c[Ha];if(null!=ya){var fe=Ha;switch(Ha){case "children":$b=ya;break;case "dangerouslySetInnerHTML":Tc=ya;break;case "style":pb(a,ya);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:za(Ha)&&"function"!==typeof ya&&"symbol"!==typeof ya&&
a.push(" ",fe,'="',w(ya),'"')}}}a.push(">");G(a,Tc,$b);return $b}}return Mb(a,c,b)}var Qb=new Map;function Kb(a){var b=Qb.get(a);void 0===b&&(b="</"+a+">",Qb.set(a,b));return b}function Rb(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}function Sb(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error(n(395));a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function Tb(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error(n(397));}}
function ac(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error(n(397));}}var bc=/[<\u2028\u2029]/g;
function cc(a){return JSON.stringify(a).replace(bc,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var dc=/[&><\u2028\u2029]/g;
function ec(a){return JSON.stringify(a).replace(dc,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var fc=!1,gc=!0;
function hc(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);gc=this.push("</style>");fc=!0;b.length=0;c.length=0}}function ic(a){return 2!==a.state?fc=!0:!1}function jc(a,b,c){fc=!1;gc=!0;b.styles.forEach(hc,a);b.stylesheets.forEach(ic);fc&&(c.stylesToHoist=!0);return gc}
function L(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var kc=[];function lc(a){J(kc,a.props);for(var b=0;b<kc.length;b++)this.push(kc[b]);kc.length=0;a.state=2}
function mc(a){var b=0<a.sheets.size;a.sheets.forEach(lc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function nc(a){if(0===a.state){a.state=1;var b=a.props;J(kc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<kc.length;a++)this.push(kc[a]);kc.length=0}}function oc(a){a.sheets.forEach(nc,this);a.sheets.clear()}
function pc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=ec(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=ec(""+d.props.href);a.push(g);e=""+e;a.push(",");e=ec(e);a.push(e);for(var h in f)if(v.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(n(399,"link"));default:a:{e=
a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!za(h))break a;g=""+g}e.push(",");k=ec(k);e.push(k);e.push(",");g=ec(g);e.push(g)}}a.push("]");
c=",[";d.state=3}});a.push("]")}
function qc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=w(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=w(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=w(JSON.stringify(e));a.push(e);for(var h in f)if(v.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(n(399,"link"));
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!za(h))break a;g=""+g}e.push(",");k=w(JSON.stringify(k));e.push(k);
e.push(",");g=w(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}function Na(a){var b=P?P:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){var e=[];c.dnsResources[a]=null;J(e,{href:a,rel:"dns-prefetch"});d.preconnects.add(e)}rc(b)}}}
function eb(a,b){var c=P?P:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){d="use-credentials"===b?d.connectResources.credentials:"string"===typeof b?d.connectResources.anonymous:d.connectResources.default;if(!d.hasOwnProperty(a)){var f=[];d[a]=null;J(f,{rel:"preconnect",href:a,crossOrigin:b});e.preconnects.add(f)}rc(c)}}}
function fb(a,b,c){var d=P?P:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}h=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(h))return;e.imageResources[h]=lb;e=[];J(e,t({rel:"preload",href:g?void 0:a,as:b},c));"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(h,e));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];J(g,t({rel:"preload",href:a,
as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?lb:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);J(g,t({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?lb:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=
e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;e=[];c=t({rel:"preload",href:a,as:b},c);switch(b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}J(e,c);g[a]=lb}rc(d)}}}
function gb(a,b){var c=P?P:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?lb:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=lb}J(f,t({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);rc(c)}}}
function hb(a,b,c){var d=P?P:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:w(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:t({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Hb(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),rc(d))}}}
function ib(a,b){var c=P?P:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=t({src:a,async:!0},b),f&&(2===f.length&&Hb(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Lb(a,b),rc(c))}}}
function jb(a,b){var c=P?P:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=t({src:a,type:"module",async:!0},b),f&&(2===f.length&&Hb(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Lb(a,b),rc(c))}}}function Hb(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}function sc(a){this.styles.add(a)}
function tc(a){this.stylesheets.add(a)}
function uc(a,b){var c=a.idPrefix;a=c+"P:";var d=c+"S:";c+="B:";var e=new Set,f=new Set,g=new Set,h=new Map,k=new Set,l=new Set,p=new Set,r={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};return{placeholderPrefix:a,segmentPrefix:d,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:[],charsetChunks:[],preconnectChunks:[],importMapChunks:[],preloadChunks:[],hoistableChunks:[],preconnects:e,fontPreloads:f,
highImagePreloads:g,styles:h,bootstrapScripts:k,scripts:l,bulkPreloads:p,preloads:r,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function vc(a,b,c,d){if(c.generateStaticMarkup)return a.push(w(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(w(b)),a=!0);return a}
var wc=Symbol.for("react.element"),xc=Symbol.for("react.portal"),yc=Symbol.for("react.fragment"),zc=Symbol.for("react.strict_mode"),Ac=Symbol.for("react.profiler"),Uc=Symbol.for("react.provider"),Vc=Symbol.for("react.context"),Wc=Symbol.for("react.server_context"),Xc=Symbol.for("react.forward_ref"),Yc=Symbol.for("react.suspense"),Zc=Symbol.for("react.suspense_list"),$c=Symbol.for("react.memo"),ad=Symbol.for("react.lazy"),bd=Symbol.for("react.scope"),cd=Symbol.for("react.debug_trace_mode"),dd=Symbol.for("react.offscreen"),
ed=Symbol.for("react.legacy_hidden"),fd=Symbol.for("react.cache"),gd=Symbol.for("react.default_value"),hd=Symbol.iterator;
function id(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case yc:return"Fragment";case xc:return"Portal";case Ac:return"Profiler";case zc:return"StrictMode";case Yc:return"Suspense";case Zc:return"SuspenseList";case fd:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Vc:return(a.displayName||"Context")+".Consumer";case Uc:return(a._context.displayName||"Context")+".Provider";case Xc:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case $c:return b=a.displayName||null,null!==b?b:id(a.type)||"Memo";case ad:b=a._payload;a=a._init;try{return id(a(b))}catch(c){}}return null}var jd={};function kd(a,b){a=a.contextTypes;if(!a)return jd;var c={},d;for(d in a)c[d]=b[d];return c}var ld=null;
function md(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error(n(401));}else{if(null===c)throw Error(n(401));md(a,c)}b.context._currentValue2=b.value}}function nd(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&nd(a)}function od(a){var b=a.parent;null!==b&&od(b);a.context._currentValue2=a.value}
function pd(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error(n(402));a.depth===b.depth?md(a,b):pd(a,b)}function qd(a,b){var c=b.parent;if(null===c)throw Error(n(402));a.depth===c.depth?md(a,c):qd(a,c);b.context._currentValue2=b.value}function rd(a){var b=ld;b!==a&&(null===b?od(a):null===a?nd(b):b.depth===a.depth?md(b,a):b.depth>a.depth?pd(b,a):qd(b,a),ld=a)}
var sd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function td(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=sd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:t({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&sd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=t({},f,h)):t(f,h))}a.state=f}else f.queue=null}
var ud={id:1,overflow:""};function vd(a,b,c){var d=a.id;a=a.overflow;var e=32-wd(d)-1;d&=~(1<<e);c+=1;var f=32-wd(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-wd(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var wd=Math.clz32?Math.clz32:xd,yd=Math.log,zd=Math.LN2;function xd(a){a>>>=0;return 0===a?32:31-(yd(a)/zd|0)|0}var Ad=Error(n(460));function Bd(){}
function Cd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Bd,Bd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Dd=b;throw Ad;}}var Dd=null;
function Ed(){if(null===Dd)throw Error(n(459));var a=Dd;Dd=null;return a}function Fd(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Gd="function"===typeof Object.is?Object.is:Fd,R=null,Hd=null,Id=null,Jd=null,Kd=null,U=null,Ld=!1,Md=!1,Nd=0,Od=0,Pd=-1,Qd=0,Rd=null,Sd=null,Td=0;function Ud(){if(null===R)throw Error(n(321));return R}function Vd(){if(0<Td)throw Error(n(312));return{memoizedState:null,queue:null,next:null}}
function Wd(){null===U?null===Kd?(Ld=!1,Kd=U=Vd()):(Ld=!0,U=Kd):null===U.next?(Ld=!1,U=U.next=Vd()):(Ld=!0,U=U.next);return U}function Xd(a,b,c,d){for(;Md;)Md=!1,Od=Nd=0,Pd=-1,Qd=0,Td+=1,U=null,c=a(b,d);Yd();return c}function Zd(){var a=Rd;Rd=null;return a}function Yd(){Jd=Id=Hd=R=null;Md=!1;Kd=null;Td=0;U=Sd=null}function $d(a,b){return"function"===typeof b?b(a):b}
function ae(a,b,c){R=Ud();U=Wd();if(Ld){var d=U.queue;b=d.dispatch;if(null!==Sd&&(c=Sd.get(d),void 0!==c)){Sd.delete(d);d=U.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);U.memoizedState=d;return[d,b]}return[U.memoizedState,b]}a=a===$d?"function"===typeof b?b():b:void 0!==c?c(b):b;U.memoizedState=a;a=U.queue={last:null,dispatch:null};a=a.dispatch=be.bind(null,R,a);return[U.memoizedState,a]}
function ce(a,b){R=Ud();U=Wd();b=void 0===b?null:b;if(null!==U){var c=U.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Gd(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();U.memoizedState=[a,b];return a}function be(a,b,c){if(25<=Td)throw Error(n(301));if(a===R)if(Md=!0,a={action:c,next:null},null===Sd&&(Sd=new Map),c=Sd.get(b),void 0===c)Sd.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}
function de(){throw Error(n(394));}function ge(){throw Error(n(479));}function he(a){var b=Qd;Qd+=1;null===Rd&&(Rd=[]);return Cd(Rd,a,b)}function ie(){throw Error(n(393));}function je(){}
var le={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return he(a);if(a.$$typeof===Vc||a.$$typeof===Wc)return a._currentValue2}throw Error(n(438,String(a)));},useContext:function(a){Ud();return a._currentValue2},useMemo:ce,useReducer:ae,useRef:function(a){R=Ud();U=Wd();var b=U.memoizedState;return null===b?(a={current:a},U.memoizedState=a):b},useState:function(a){return ae($d,a)},useInsertionEffect:je,useLayoutEffect:je,
useCallback:function(a,b){return ce(function(){return a},b)},useImperativeHandle:je,useEffect:je,useDebugValue:je,useDeferredValue:function(a){Ud();return a},useTransition:function(){Ud();return[!1,de]},useId:function(){var a=Hd.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-wd(a)-1)).toString(32)+b;var c=ke;if(null===c)throw Error(n(404));b=Nd++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error(n(407));return c()},useCacheRefresh:function(){return ie},
useHostTransitionStatus:function(){Ud();return La},useOptimistic:function(a){Ud();return[a,ge]},useFormState:function(a,b,c){Ud();var d=Od++,e=Id;if("function"===typeof a.$$FORM_ACTION){var f=null,g=Jd;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,null,d]),0),k===f&&(Pd=d,b=e[0]))}var l=a.bind(null,b);a=function(r){l(r)};"function"===typeof l.$$FORM_ACTION&&(a.$$FORM_ACTION=function(r){r=
l.$$FORM_ACTION(r);void 0!==c&&(c+="",r.action=c);var m=r.data;m&&(null===f&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,null,d]),0)),m.append("$ACTION_KEY",f));return r});return[b,a]}var p=a.bind(null,b);return[b,function(r){p(r)}]}},ke=null,me={getCacheSignal:function(){throw Error(n(248));},getCacheForType:function(){throw Error(n(248));}},ne=Ka.ReactCurrentDispatcher,oe=Ka.ReactCurrentCache;function pe(a){console.error(a);return null}function qe(){}
function re(a,b,c,d,e,f,g,h,k,l,p,r){Ma.current=kb;var m=[],H=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:H,pingedTasks:m,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?pe:f,onPostpone:void 0===p?qe:p,onAllReady:void 0===g?
qe:g,onShellReady:void 0===h?qe:h,onShellError:void 0===k?qe:k,onFatalError:void 0===l?qe:l,formState:void 0===r?null:r};c=se(b,0,null,d,!1,!1);c.parentFlushed=!0;a=te(b,null,a,-1,null,c,H,null,d,jd,null,ud);m.push(a);return b}var P=null;function ue(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,ve(a))}
function we(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function te(a,b,c,d,e,f,g,h,k,l,p,r){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var m={replay:null,node:c,childIndex:d,ping:function(){return ue(a,m)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:p,treeContext:r,thenableState:b};g.add(m);return m}
function xe(a,b,c,d,e,f,g,h,k,l,p,r){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var m={replay:c,node:d,childIndex:e,ping:function(){return ue(a,m)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:p,treeContext:r,thenableState:b};g.add(m);return m}function se(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function V(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function ye(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function ze(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error(n(108,id(e)||"Unknown",h));e=t({},c,d)}b.legacyContext=e;X(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,X(a,b,null,f,-1),b.keyPath=e}
function Ae(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=vd(c,1,0),Y(a,b,d,-1),b.treeContext=c):h?Y(a,b,d,-1):X(a,b,null,d,-1);b.keyPath=f}function Be(a,b){if(a&&a.defaultProps){b=t({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Ce(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=kd(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:d);td(h,e,f,d);ze(a,b,c,h,e)}else{h=kd(e,b.legacyContext);R={};Hd=b;Id=a;Jd=c;Od=Nd=0;Pd=-1;Qd=0;Rd=d;d=e(f,h);d=Xd(e,f,d,h);g=0!==Nd;var k=Od,l=Pd;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(td(d,e,f,h),ze(a,b,c,d,e)):Ae(a,b,c,d,g,k,l)}else if("string"===typeof e)if(d=
b.blockedSegment,null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=nb(h,e,f),b.keyPath=c,Y(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Pb(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;k=b.keyPath;b.formatContext=nb(h,e,f);b.keyPath=c;Y(a,b,g,-1);b.formatContext=h;b.keyPath=k;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(Kb(e))}d.lastPushedText=!1}else{switch(e){case ed:case cd:case zc:case Ac:case yc:e=b.keyPath;b.keyPath=c;X(a,b,null,f.children,-1);b.keyPath=e;return;case dd:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,X(a,b,null,f.children,-1),b.keyPath=e);return;case Zc:e=b.keyPath;b.keyPath=c;X(a,b,null,f.children,-1);b.keyPath=e;return;case bd:throw Error(n(343));case Yc:a:if(null!==
b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Y(a,b,c,-1)}finally{b.keyPath=e}}else{l=b.keyPath;e=b.blockedBoundary;var p=b.blockedSegment;d=f.fallback;var r=f.children;f=new Set;g=we(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);k=se(a,p.chunks.length,g,b.formatContext,!1,!1);p.children.push(k);p.lastPushedText=!1;var m=se(a,0,null,b.formatContext,!1,!1);m.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=m;a.renderState.boundaryResources=g.resources;b.keyPath=c;try{if(Y(a,
b,r,-1),a.renderState.generateStaticMarkup||m.lastPushedText&&m.textEmbedded&&m.chunks.push("\x3c!-- --\x3e"),m.status=1,De(g,m),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(H){m.status=4,g.status=4,h=V(a,H),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=p,b.keyPath=l}h=[c[0],"Suspense Fallback",c[2]];l=a.trackedPostpones;null!==l&&(p=[h[1],h[2],[],null],l.workingMap.set(h,p),5===g.status?l.workingMap.get(c)[4]=p:g.trackedFallbackNode=
p);b=te(a,null,d,-1,e,k,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Xc:e=e.render;R={};Hd=b;Id=a;Jd=c;Od=Nd=0;Pd=-1;Qd=0;Rd=d;d=e(f,g);f=Xd(e,f,d,g);Ae(a,b,c,f,0!==Nd,Od,Pd);return;case $c:e=e.type;f=Be(e,f);Ce(a,b,c,d,e,f,g);return;case Uc:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue2;e._currentValue2=f;k=ld;ld=f={parent:k,depth:null===k?0:k.depth+1,context:e,parentValue:g,
value:f};b.context=f;b.keyPath=c;X(a,b,null,h,-1);a=ld;if(null===a)throw Error(n(403));c=a.parentValue;a.context._currentValue2=c===gd?a.context._defaultValue:c;a=ld=a.parent;b.context=a;b.keyPath=d;return;case Vc:f=f.children;f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;X(a,b,null,f,-1);b.keyPath=e;return;case ad:h=e._init;e=h(e._payload);f=Be(e,f);Ce(a,b,c,d,e,f,void 0);return}throw Error(n(130,null==e?e:typeof e,""));}}
function Ee(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=se(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Y(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(De(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function X(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Ee(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case wc:var f=d.type,g=d.key,h=d.props,k=d.ref,l=id(f),p=null==g?-1===e?0:e:g;g=[b.keyPath,l,p];if(null!==b.replay)a:{var r=b.replay;e=r.nodes;for(d=0;d<e.length;d++){var m=e[d];if(p===m[1]){if(4===m.length){if(null!==l&&l!==m[0])throw Error(n(490,m[0],l));l=m[2];m=m[3];p=b.node;b.replay={nodes:l,slots:m,pendingTasks:1};
try{Ce(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(n(488));b.replay.pendingTasks--}catch(q){if("object"===typeof q&&null!==q&&(q===Ad||"function"===typeof q.then))throw b.node===p&&(b.replay=r),q;b.replay.pendingTasks--;g=a;a=b.blockedBoundary;c=q;h=V(g,c);Fe(g,a,l,m,c,h)}b.replay=r}else{if(f!==Yc)throw Error(n(490,"Suspense",id(f)||"Unknown"));b:{r=void 0;c=m[5];f=m[2];k=m[3];l=null===m[4]?[]:m[4][2];m=null===m[4]?null:m[4][3];p=b.keyPath;var H=b.replay,B=b.blockedBoundary,
S=h.children;h=h.fallback;var u=new Set,x=we(a,u);x.parentFlushed=!0;x.rootSegmentID=c;b.blockedBoundary=x;b.replay={nodes:f,slots:k,pendingTasks:1};a.renderState.boundaryResources=x.resources;try{Y(a,b,S,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(n(488));b.replay.pendingTasks--;if(0===x.pendingTasks&&0===x.status){x.status=1;a.completedBoundaries.push(x);break b}}catch(q){x.status=4,r=V(a,q),x.errorDigest=r,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(x)}finally{a.renderState.boundaryResources=
B?B.resources:null,b.blockedBoundary=B,b.replay=H,b.keyPath=p}b=xe(a,null,{nodes:l,slots:m,pendingTasks:0},h,-1,B,u,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else Ce(a,b,g,c,f,h,k);return;case xc:throw Error(n(257));case ad:h=d._init;d=h(d._payload);X(a,b,null,d,e);return}if(Ja(d)){Ge(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=hd&&d[hd]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&
(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);Ge(a,b,g,e)}return}if("function"===typeof d.then)return X(a,b,null,he(d),e);if(d.$$typeof===Vc||d.$$typeof===Wc)return X(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);throw Error(n(31,"[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e));}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=vc(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&
(e=b.blockedSegment,null!==e&&(e.lastPushedText=vc(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function Ge(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{Ge(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(n(488));b.replay.pendingTasks--}catch(r){if("object"===typeof r&&null!==r&&(r===Ad||"function"===typeof r.then))throw r;b.replay.pendingTasks--;c=a;var l=b.blockedBoundary,p=r;a=V(c,p);Fe(c,
l,d,k,p,a)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)k=c[d],b.treeContext=vd(f,g,d),l=h[d],"number"===typeof l?(Ee(a,b,l,k,d),delete h[d]):Y(a,b,k,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=vd(f,g,h),Y(a,b,d,h);b.treeContext=f;b.keyPath=e}
function Y(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.blockedSegment;if(null===l)try{return X(a,b,null,c,d)}catch(m){if(Yd(),c=m===Ad?Ed():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Zd();a=xe(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;rd(g);return}}else{var p=
l.children.length,r=l.chunks.length;try{return X(a,b,null,c,d)}catch(m){if(Yd(),l.children.length=p,l.chunks.length=r,c=m===Ad?Ed():m,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Zd();l=b.blockedSegment;p=se(a,l.chunks.length,null,b.formatContext,l.lastPushedText,!0);l.children.push(p);l.lastPushedText=!1;a=te(a,d,b.node,b.childIndex,b.blockedBoundary,p,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;rd(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;rd(g);throw c;}function He(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,Ie(this,b,a))}
function Fe(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Fe(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,p=we(k,new Set);p.parentFlushed=!0;p.rootSegmentID=h;p.status=4;p.errorDigest=l;p.parentFlushed&&k.clientRenderedBoundaries.push(p)}}c.length=0;if(null!==d){if(null===b)throw Error(n(487));4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var r in d)delete d[r]}}
function Je(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(1!==b.status&&2!==b.status){a=a.replay;if(null===a){V(b,c);ye(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=V(b,c),Fe(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&(b.onShellError=qe,a=b.onShellReady,a())}}else d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=V(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return Je(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&(a=b.onAllReady,a())}function De(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&De(a,c)}else a.completedSegments.push(b)}
function Ie(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error(n(389));a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&(a.onShellError=qe,b=a.onShellReady,b())}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&De(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(He,a),b.fallbackAbortableTasks.clear())):null!==
c&&c.parentFlushed&&1===c.status&&(De(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&(a=a.onAllReady,a())}
function ve(a){if(2!==a.status){var b=ld,c=ne.current;ne.current=le;var d=oe.current;oe.current=me;var e=P;P=a;var f=ke;ke=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,p=k.blockedBoundary;l.renderState.boundaryResources=p?p.resources:null;var r=k.blockedSegment;if(null===r){var m=l;if(0!==k.replay.pendingTasks){rd(k.context);try{var H=k.thenableState;k.thenableState=null;X(m,k,H,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error(n(488));
k.replay.pendingTasks--;k.abortSet.delete(k);Ie(m,k.blockedBoundary,null)}catch(A){Yd();var B=A===Ad?Ed():A;if("object"===typeof B&&null!==B&&"function"===typeof B.then){var S=k.ping;B.then(S,S);k.thenableState=Zd()}else{k.replay.pendingTasks--;k.abortSet.delete(k);l=void 0;var u=m,x=k.blockedBoundary,q=B,Z=k.replay.nodes,I=k.replay.slots;l=V(u,q);Fe(u,x,Z,I,q,l);m.pendingRootTasks--;if(0===m.pendingRootTasks){m.onShellError=qe;var aa=m.onShellReady;aa()}m.allPendingTasks--;if(0===m.allPendingTasks){var C=
m.onAllReady;C()}}}finally{m.renderState.boundaryResources=null}}}else if(m=void 0,u=r,0===u.status){rd(k.context);var M=u.children.length,z=u.chunks.length;try{var na=k.thenableState;k.thenableState=null;X(l,k,na,k.node,k.childIndex);l.renderState.generateStaticMarkup||u.lastPushedText&&u.textEmbedded&&u.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);u.status=1;Ie(l,k.blockedBoundary,u)}catch(A){Yd();u.children.length=M;u.chunks.length=z;var T=A===Ad?Ed():A;if("object"===typeof T&&null!==T&&
"function"===typeof T.then){var ba=k.ping;T.then(ba,ba);k.thenableState=Zd()}else{k.abortSet.delete(k);u.status=4;var N=k.blockedBoundary;m=V(l,T);null===N?ye(l,T):(N.pendingTasks--,4!==N.status&&(N.status=4,N.errorDigest=m,N.parentFlushed&&l.clientRenderedBoundaries.push(N)));l.allPendingTasks--;if(0===l.allPendingTasks){var W=l.onAllReady;W()}}}finally{l.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&Ke(a,a.destination)}catch(A){V(a,A),ye(a,A)}finally{ke=f,ne.current=c,
oe.current=d,c===le&&rd(b),P=e}}}
function Le(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=Me(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error(n(390));
}}
function Me(a,b,c){var d=c.boundary;if(null===d)return Le(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=w(d),b.push(d),b.push('"')),b.push("></template>")),Le(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Sb(b,a.renderState,
d.rootSegmentID),Le(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Sb(b,a.renderState,d.rootSegmentID),Le(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(sc,e),c.stylesheets.forEach(tc,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error(n(391));Me(a,b,c[0]);a=a.renderState.generateStaticMarkup?
!0:b.push("\x3c!--/$--\x3e");return a}function Ne(a,b,c){Tb(b,a.renderState,c.parentFormatContext,c.id);Me(a,b,c);return ac(b,c.parentFormatContext)}
function Oe(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)Pe(a,b,c,d[e]);d.length=0;jc(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),pc(b,c)):(b.push('" data-sty="'),qc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Rb(b,a)&&d}
function Pe(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error(n(392));return Ne(a,b,d)}if(e===c.rootSegmentID)return Ne(a,b,d);Ne(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function Ke(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,p=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(p)for(f=0;f<p.length;f++)b.push(p[f]);else{var r=K("head");b.push(r);
b.push(">")}}else if(p)for(f=0;f<p.length;f++)b.push(p[f]);var m=e.charsetChunks;for(f=0;f<m.length;f++)b.push(m[f]);m.length=0;e.preconnects.forEach(L,b);e.preconnects.clear();var H=e.preconnectChunks;for(f=0;f<H.length;f++)b.push(H[f]);H.length=0;e.fontPreloads.forEach(L,b);e.fontPreloads.clear();e.highImagePreloads.forEach(L,b);e.highImagePreloads.clear();e.styles.forEach(mc,b);var B=e.importMapChunks;for(f=0;f<B.length;f++)b.push(B[f]);B.length=0;e.bootstrapScripts.forEach(L,b);e.scripts.forEach(L,
b);e.scripts.clear();e.bulkPreloads.forEach(L,b);e.bulkPreloads.clear();var S=e.preloadChunks;for(f=0;f<S.length;f++)b.push(S[f]);S.length=0;var u=e.hoistableChunks;for(f=0;f<u.length;f++)b.push(u[f]);u.length=0;if(l&&null===p){var x=Kb("head");b.push(x)}Me(a,b,d);a.completedRootSegment=null;Rb(b,a.renderState)}else return;var q=a.renderState;d=0;q.preconnects.forEach(L,b);q.preconnects.clear();var Z=q.preconnectChunks;for(d=0;d<Z.length;d++)b.push(Z[d]);Z.length=0;q.fontPreloads.forEach(L,b);q.fontPreloads.clear();
q.highImagePreloads.forEach(L,b);q.highImagePreloads.clear();q.styles.forEach(oc,b);q.scripts.forEach(L,b);q.scripts.clear();q.bulkPreloads.forEach(L,b);q.bulkPreloads.clear();var I=q.preloadChunks;for(d=0;d<I.length;d++)b.push(I[d]);I.length=0;var aa=q.hoistableChunks;for(d=0;d<aa.length;d++)b.push(aa[d]);aa.length=0;var C=a.clientRenderedBoundaries;for(c=0;c<C.length;c++){var M=C[c];q=b;var z=a.resumableState,na=a.renderState,T=M.rootSegmentID,ba=M.errorDigest,N=M.errorMessage,W=M.errorComponentStack,
A=0===z.streamingFormat;A?(q.push(na.startInlineScript),0===(z.instructions&4)?(z.instructions|=4,q.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):q.push('$RX("')):q.push('<template data-rxi="" data-bid="');q.push(na.boundaryPrefix);var Oa=T.toString(16);q.push(Oa);A&&q.push('"');if(ba||N||W)if(A){q.push(",");var Pa=cc(ba||"");q.push(Pa)}else{q.push('" data-dgst="');
var Qa=w(ba||"");q.push(Qa)}if(N||W)if(A){q.push(",");var oa=cc(N||"");q.push(oa)}else{q.push('" data-msg="');var Q=w(N||"");q.push(Q)}if(W)if(A){q.push(",");var qb=cc(W);q.push(qb)}else{q.push('" data-stck="');var pa=w(W);q.push(pa)}if(A?!q.push(")\x3c/script>"):!q.push('"></template>')){a.destination=null;c++;C.splice(0,c);return}}C.splice(0,c);var qa=a.completedBoundaries;for(c=0;c<qa.length;c++)if(!Oe(a,b,qa[c])){a.destination=null;c++;qa.splice(0,c);return}qa.splice(0,c);var ca=a.partialBoundaries;
for(c=0;c<ca.length;c++){var ra=ca[c];a:{C=a;M=b;C.renderState.boundaryResources=ra.resources;var sa=ra.completedSegments;for(z=0;z<sa.length;z++)if(!Pe(C,M,ra,sa[z])){z++;sa.splice(0,z);var Ra=!1;break a}sa.splice(0,z);Ra=jc(M,ra.resources,C.renderState)}if(!Ra){a.destination=null;c++;ca.splice(0,c);return}}ca.splice(0,c);var ta=a.completedBoundaries;for(c=0;c<ta.length;c++)if(!Oe(a,b,ta[c])){a.destination=null;c++;ta.splice(0,c);return}ta.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&
0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(ca=Kb("body"),b.push(ca)),c.hasHtml&&(c=Kb("html"),b.push(c)),b.push(null),a.destination=null)}}function rc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?Ke(a,b):a.flushScheduled=!1}}
function Qe(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error(n(432)):b;c.forEach(function(e){return Je(e,a,d)});c.clear()}null!==a.destination&&Ke(a,a.destination)}catch(e){V(a,e),ye(a,e)}}function Re(){}
function Se(a,b,c,d){var e=!1,f=null,g="",h={push:function(l){null!==l&&(g+=l);return!0},destroy:function(l){e=!0;f=l}},k=!1;b=mb(b?b.identifierPrefix:void 0,void 0);a=re(a,b,uc(b,c),y(0,null,0),Infinity,Re,void 0,function(){k=!0},void 0,void 0,void 0);a.flushScheduled=null!==a.destination;ve(a);Qe(a,d);if(1===a.status)a.status=2,h.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=h;try{Ke(a,h)}catch(l){V(a,l),ye(a,l)}}if(e&&f!==d)throw f;if(!k)throw Error(n(426));return g}
exports.renderToNodeStream=function(){throw Error(n(207));};exports.renderToStaticMarkup=function(a,b){return Se(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};exports.renderToStaticNodeStream=function(){throw Error(n(208));};exports.renderToString=function(a,b){return Se(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};
exports.version="18.3.0-canary-8c8ee9ee6-20231026";
