{"version": 3, "sources": ["../../../../src/server/future/route-matcher-providers/app-route-route-matcher-provider.ts"], "names": ["AppRouteRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "APP_PATHS_MANIFEST", "normalizers", "AppNormalizers", "transform", "manifest", "pages", "Object", "keys", "filter", "page", "isAppRouteRoute", "matchers", "filename", "normalize", "pathname", "bundlePath", "push", "AppRouteRouteMatcher", "kind", "RouteKind", "APP_ROUTE"], "mappings": ";;;;+BAWaA;;;eAAAA;;;iCAXmB;2BACG;2BACT;sCACW;8CAKQ;qBACd;AAExB,MAAMA,qCAAqCC,0DAA4B;IAG5EC,YAAYC,OAAe,EAAEC,cAA8B,CAAE;QAC3D,KAAK,CAACC,6BAAkB,EAAED;QAE1B,IAAI,CAACE,WAAW,GAAG,IAAIC,mBAAc,CAACJ;IACxC;IAEA,MAAgBK,UACdC,QAAkB,EAC4B;QAC9C,wCAAwC;QACxC,MAAMC,QAAQC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,OAASC,IAAAA,gCAAe,EAACD;QAErE,qBAAqB;QACrB,MAAME,WAAwC,EAAE;QAChD,KAAK,MAAMF,QAAQJ,MAAO;YACxB,MAAMO,WAAW,IAAI,CAACX,WAAW,CAACW,QAAQ,CAACC,SAAS,CAACT,QAAQ,CAACK,KAAK;YACnE,MAAMK,WAAW,IAAI,CAACb,WAAW,CAACa,QAAQ,CAACD,SAAS,CAACJ;YACrD,MAAMM,aAAa,IAAI,CAACd,WAAW,CAACc,UAAU,CAACF,SAAS,CAACJ;YAEzDE,SAASK,IAAI,CACX,IAAIC,0CAAoB,CAAC;gBACvBC,MAAMC,oBAAS,CAACC,SAAS;gBACzBN;gBACAL;gBACAM;gBACAH;YACF;QAEJ;QAEA,OAAOD;IACT;AACF"}