/**
 * @license React
 * react-server-dom-webpack-client.node.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var p=require("util"),r=require("react-dom"),u=require("react"),v={stream:!0};function w(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var x=new Map;
function y(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function z(){}
function A(a){for(var b=a[1],c=[],d=0;d<b.length;){var g=b[d++];b[d++];var h=x.get(g);if(void 0===h){h=__webpack_chunk_load__(g);c.push(h);var l=x.set.bind(x,g,null);h.then(l,z);x.set(g,h)}else null!==h&&c.push(h)}return 4===a.length?0===c.length?y(a[0]):Promise.all(c).then(function(){return y(a[0])}):0<c.length?Promise.all(c):null}
function B(a,b,c){if(null!==a)for(var d=1;d<b.length;d+=2){var g=c,h=C.current;if(h){var l=h.preinitScript,k=a.prefix+b[d];var e=a.crossOrigin;e="string"===typeof e?"use-credentials"===e?e:"":void 0;l.call(h,k,{crossOrigin:e,nonce:g})}}}var C=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,D=Symbol.for("react.element"),F=Symbol.for("react.provider"),aa=Symbol.for("react.server_context"),ba=Symbol.for("react.lazy"),G=Symbol.for("react.default_value"),H=Symbol.iterator;
function ca(a){if(null===a||"object"!==typeof a)return null;a=H&&a[H]||a["@@iterator"];return"function"===typeof a?a:null}var da=Array.isArray,I=Object.getPrototypeOf,ea=Object.prototype,J=new WeakMap;function fa(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ha(a,b,c,d){function g(e,f){if(null===f)return null;if("object"===typeof f){if("function"===typeof f.then){null===k&&(k=new FormData);l++;var t=h++;f.then(function(n){n=JSON.stringify(n,g);var q=k;q.append(b+t,n);l--;0===l&&c(q)},function(n){d(n)});return"$@"+t.toString(16)}if(da(f))return f;if(f instanceof FormData){null===k&&(k=new FormData);var E=k;e=h++;var m=b+e+"_";f.forEach(function(n,q){E.append(m+q,n)});return"$K"+e.toString(16)}if(f instanceof Map)return f=JSON.stringify(Array.from(f),
g),null===k&&(k=new FormData),e=h++,k.append(b+e,f),"$Q"+e.toString(16);if(f instanceof Set)return f=JSON.stringify(Array.from(f),g),null===k&&(k=new FormData),e=h++,k.append(b+e,f),"$W"+e.toString(16);if(ca(f))return Array.from(f);e=I(f);if(e!==ea&&(null===e||null!==I(e)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return f}if("string"===typeof f){if("Z"===f[f.length-1]&&this[e]instanceof Date)return"$D"+f;
f="$"===f[0]?"$"+f:f;return f}if("boolean"===typeof f)return f;if("number"===typeof f)return fa(f);if("undefined"===typeof f)return"$undefined";if("function"===typeof f){f=J.get(f);if(void 0!==f)return f=JSON.stringify(f,g),null===k&&(k=new FormData),e=h++,k.set(b+e,f),"$F"+e.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof f){e=f.description;if(Symbol.for(e)!==f)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(f.description+") cannot be found among global symbols."));return"$S"+e}if("bigint"===typeof f)return"$n"+f.toString(10);throw Error("Type "+typeof f+" is not supported as an argument to a Server Function.");}var h=1,l=0,k=null;a=JSON.stringify(a,g);null===k?c(a):(k.set(b+"0",a),0===l&&c(k))}var K=new WeakMap;
function ia(a){var b,c,d=new Promise(function(g,h){b=g;c=h});ha(a,"",function(g){if("string"===typeof g){var h=new FormData;h.append("0",g);g=h}d.status="fulfilled";d.value=g;b(g)},function(g){d.status="rejected";d.reason=g;c(g)});return d}
function ja(a){var b=J.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var c=null;if(null!==b.bound){c=K.get(b);c||(c=ia(b),K.set(b,c));if("rejected"===c.status)throw c.reason;if("fulfilled"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(g,h){d.append("$ACTION_"+a+":"+h,g)});c=d;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:c}}
function ka(a,b){var c=J.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case "fulfilled":return d.value.length===b;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(g){d.status="fulfilled";d.value=g},function(g){d.status="rejected";d.reason=g})),d;}}
function L(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:ja},$$IS_SIGNATURE_EQUAL:{value:ka},bind:{value:la}});J.set(a,b)}var ma=Function.prototype.bind,na=Array.prototype.slice;function la(){var a=ma.apply(this,arguments),b=J.get(this);if(b){var c=na.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(g){return g.concat(c)}):Promise.resolve(c);L(a,{id:b.id,bound:d})}return a}
function oa(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}L(c,{id:a,bound:null});return c}var M=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function N(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}N.prototype=Object.create(Promise.prototype);
N.prototype.then=function(a,b){switch(this.status){case "resolved_model":O(this);break;case "resolved_module":P(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function pa(a){switch(a.status){case "resolved_model":O(a);break;case "resolved_module":P(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function Q(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function R(a,b,c){switch(a.status){case "fulfilled":Q(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=c;break;case "rejected":c&&Q(c,a.reason)}}
function S(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&Q(c,b)}}function T(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(P(a),R(a,c,d))}}var U=null,V=null;
function O(a){var b=U,c=V;U=a;V=null;var d=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var g=JSON.parse(d,a._response._fromJSON);if(null!==V&&0<V.deps)V.value=g,a.status="blocked",a.value=null,a.reason=null;else{var h=a.value;a.status="fulfilled";a.value=g;null!==h&&Q(h,g)}}catch(l){a.status="rejected",a.reason=l}finally{U=b,V=c}}
function P(a){try{var b=a.value,c=globalThis.__next_require__(b[0]);if(4===b.length&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b[2]?c:""===b[2]?c.__esModule?c.default:c:c[b[2]];a.status="fulfilled";a.value=d}catch(g){a.status="rejected",a.reason=g}}function W(a,b){a._chunks.forEach(function(c){"pending"===c.status&&S(c,b)})}function X(a,b){var c=a._chunks,d=c.get(b);d||(d=new N("pending",null,null,a),c.set(b,d));return d}
function qa(a,b,c,d){if(V){var g=V;d||g.deps++}else g=V={deps:d?0:1,value:null};return function(h){b[c]=h;g.deps--;0===g.deps&&"blocked"===a.status&&(h=a.value,a.status="fulfilled",a.value=g.value,null!==h&&Q(h,g.value))}}function ra(a){return function(b){return S(a,b)}}
function sa(a,b){function c(){var g=Array.prototype.slice.call(arguments),h=b.bound;return h?"fulfilled"===h.status?d(b.id,h.value.concat(g)):Promise.resolve(h).then(function(l){return d(b.id,l.concat(g))}):d(b.id,g)}var d=a._callServer;L(c,b);return c}function Y(a,b){a=X(a,b);switch(a.status){case "resolved_model":O(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ta(a,b,c,d){if("$"===d[0]){if("$"===d)return D;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=X(a,b),{$$typeof:ba,_payload:a,_init:pa};case "@":return b=parseInt(d.slice(2),16),X(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),M[a]||(b={$$typeof:aa,_currentValue:G,_currentValue2:G,_defaultValue:G,_threadCount:0,Provider:null,Consumer:null,_globalName:a},b.Provider={$$typeof:F,_context:b},M[a]=b),M[a].Provider;case "F":return b=
parseInt(d.slice(2),16),b=Y(a,b),sa(a,b);case "Q":return b=parseInt(d.slice(2),16),a=Y(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=Y(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=X(a,d);switch(a.status){case "resolved_model":O(a);break;case "resolved_module":P(a)}switch(a.status){case "fulfilled":return a.value;
case "pending":case "blocked":case "cyclic":return d=U,a.then(qa(d,b,c,"cyclic"===a.status),ra(d)),null;default:throw a.reason;}}}return d}function ua(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}
function va(a,b,c,d){var g=new Map;a={_bundlerConfig:a,_moduleLoading:b,_callServer:void 0!==c?c:ua,_nonce:d,_chunks:g,_stringDecoder:new p.TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=wa(a);return a}
function xa(a,b,c){var d=a._chunks,g=d.get(b);c=JSON.parse(c,a._fromJSON);var h=w(a._bundlerConfig,c);B(a._moduleLoading,c[1],a._nonce);if(c=A(h)){if(g){var l=g;l.status="blocked"}else l=new N("blocked",null,null,a),d.set(b,l);c.then(function(){return T(l,h)},function(k){return S(l,k)})}else g?T(g,h):d.set(b,new N("resolved_module",h,null,a))}
function wa(a){return function(b,c){return"string"===typeof c?ta(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===D?{$$typeof:D,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}function Z(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
exports.createFromNodeStream=function(a,b,c){var d=va(b.moduleMap,b.moduleLoading,Z,c&&"string"===typeof c.nonce?c.nonce:void 0);a.on("data",function(g){for(var h=0,l=d._rowState,k=d._rowID,e=d._rowTag,f=d._rowLength,t=d._buffer,E=g.length;h<E;){var m=-1;switch(l){case 0:m=g[h++];58===m?l=1:k=k<<4|(96<m?m-87:m-48);continue;case 1:l=g[h];84===l?(e=l,l=2,h++):64<l&&91>l?(e=l,l=3,h++):(e=0,l=3);continue;case 2:m=g[h++];44===m?l=4:f=f<<4|(96<m?m-87:m-48);continue;case 3:m=g.indexOf(10,h);break;case 4:m=
h+f,m>g.length&&(m=-1)}var n=g.byteOffset+h;if(-1<m){f=new Uint8Array(g.buffer,n,m-h);h=e;n=d._stringDecoder;e="";for(var q=0;q<t.length;q++)e+=n.decode(t[q],v);e+=n.decode(f);switch(h){case 73:xa(d,k,e);break;case 72:k=e[0];e=e.slice(1);e=JSON.parse(e,d._fromJSON);if(f=C.current)switch(k){case "D":f.prefetchDNS(e);break;case "C":"string"===typeof e?f.preconnect(e):f.preconnect(e[0],e[1]);break;case "L":k=e[0];h=e[1];3===e.length?f.preload(k,h,e[2]):f.preload(k,h);break;case "m":"string"===typeof e?
f.preloadModule(e):f.preloadModule(e[0],e[1]);break;case "S":"string"===typeof e?f.preinitStyle(e):f.preinitStyle(e[0],0===e[1]?void 0:e[1],3===e.length?e[2]:void 0);break;case "X":"string"===typeof e?f.preinitScript(e):f.preinitScript(e[0],e[1]);break;case "M":"string"===typeof e?f.preinitModuleScript(e):f.preinitModuleScript(e[0],e[1])}break;case 69:e=JSON.parse(e);f=e.digest;e=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
e.stack="Error: "+e.message;e.digest=f;f=d._chunks;(h=f.get(k))?S(h,e):f.set(k,new N("rejected",null,e,d));break;case 84:d._chunks.set(k,new N("fulfilled",e,null,d));break;default:f=d._chunks,(h=f.get(k))?(k=h,"pending"===k.status&&(f=k.value,h=k.reason,k.status="resolved_model",k.value=e,null!==f&&(O(k),R(k,f,h)))):f.set(k,new N("resolved_model",e,null,d))}h=m;3===l&&h++;f=k=e=l=0;t.length=0}else{g=new Uint8Array(g.buffer,n,g.byteLength-h);t.push(g);f-=g.byteLength;break}}d._rowState=l;d._rowID=
k;d._rowTag=e;d._rowLength=f});a.on("error",function(g){W(d,g)});a.on("end",function(){W(d,Error("Connection closed."))});return X(d,0)};exports.createServerReference=function(a){return oa(a,Z)};
